FROM python:3.12-slim

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

RUN apt-get update && apt-get install -y --no-install-recommends \
    libgomp1 curl ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -ms /bin/bash appuser
WORKDIR /app

# Install dependencies first for better caching
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt && \
    pip install gunicorn eventlet python-dotenv

# Copy project
COPY . .

RUN mkdir -p /app/logs /app/data && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

# Production entrypoint (Gunicorn + eventlet)
CMD ["gunicorn", "-c", "gunicorn.conf.py", "main:app"]