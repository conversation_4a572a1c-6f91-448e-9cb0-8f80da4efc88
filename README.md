# TradeLab FPC

Production-ready containerization for a Flask + Socket.IO app, fronted by <PERSON><PERSON><PERSON>. Includes FYERS integration for market data, static assets, and a live chart UI.

This README covers how to run in Docker (production-style), configure environment variables, complete FYERS auth, run locally for development, available endpoints and Socket.IO events, and common troubleshooting.

## Architecture
- Flask app (`main.py`) with routes in `app/routes.py` and Socket.IO handlers in `app/socket_events.py`.
- Gunicorn with `eventlet` worker to support WebSockets efficiently.
- Nginx reverse proxy serving static files and proxying both HTTP and Socket.IO upgrades.
- Docker Compose to orchestrate app + Nginx, with volumes for persistent data and logs.

Key files:
- `Dockerfile` – Python 3.12-slim base; installs deps; runs Gunicorn.
- `gunicorn.conf.py` – eventlet worker, timeouts, logging.
- `nginx/conf.d/app.conf` – serves `/static`, proxies `/socket.io/` and all other routes to the app.
- `docker-compose.yml` – services, env, volumes, healthcheck.
- `.env.example` – environment variable template.

## Quick start (Docker)
On Windows PowerShell:
1) Copy env template and edit values
    - Copy the example env file and set your secrets/keys.
    - At minimum, set `SECRET_KEY` and the FYERS values if you’ll use live data.

    PowerShell:
    - `Copy-Item .env.example .env`

2) Build and run
    - `docker compose up -d --build`
    - `podman compose build`
    - `podman compose up -d`

3) Open the app
    - Browse to http://localhost

4) FYERS login (for live data)
    - Go to http://localhost/login and finish the FYERS OAuth flow.
    - The redirect callback is `/fyers/callback` (see “FYERS setup” below).

## Configuration (.env)
These are read by the app at startup:

- Flask / server
   - `FLASK_ENV` – `production` or `development` (defaults to development when running `python main.py`).
   - `SECRET_KEY` – required; set a strong random value in production.
   - `APP_HOST`, `APP_PORT` – only used for local dev (`python main.py`).

- Socket.IO
   - `SOCKETIO_ASYNC_MODE` – recommended `eventlet` in production (already set in compose).
   - `CORS_ORIGINS` – `*` allows all; for production set to your site origins, comma-separated (e.g., `https://example.com,https://www.example.com`).

- FYERS API
   - `FYERS_CLIENT_ID`
   - `FYERS_SECRET_KEY`
   - `FYERS_REDIRECT_URI` – must exactly match the redirect URL registered in FYERS Developer Console (see below).

- Logging
   - `LOG_LEVEL` – e.g., `INFO`, `DEBUG`.

Volumes used by Compose:
- `./data` → `/app/data` (persists FYERS token at `data/token.json`, symbol db, etc.)
- `./logs` → `/app/logs` (application logs)
- `./logs/nginx` → `/var/log/nginx` (Nginx access/error logs)

## FYERS setup (OAuth)
To receive live market data:
1) Create an app in FYERS Developer Console and obtain `CLIENT_ID` and `SECRET_KEY`.
2) Configure a redirect URL in FYERS that matches one you’ll actually use:
    - Local Docker: `http://localhost/fyers/callback`
    - Production domain (example): `https://your-domain.com/fyers/callback`
3) Put those values into `.env` (`FYERS_CLIENT_ID`, `FYERS_SECRET_KEY`, `FYERS_REDIRECT_URI`).
4) Start the stack, then visit `/login`. On success you’ll be redirected to the chart and a token will be stored at `data/token.json`.

## Endpoints
UI pages:
- `GET /` – Base page
- `GET /chart` – Live chart UI

Auth:
- `GET /login` – Start FYERS auth flow
- `GET /fyers/callback` – FYERS OAuth callback; persists token
- `GET /logout` – Clear token and redirect to `/`

APIs:
- `GET /api/symbols?q=&category=&limit=` – Symbol search
- `GET /api/watchlist?q=&limit=` – List current watchlist (ensures `watchlisted: true`)
- `POST /api/watchlist` – Add to watchlist; body: `{ symbol, description?, exchange?, type? }`
- `DELETE /api/watchlist/<symbol>` – Remove from watchlist
- `GET /api/symbols/by-expiry?expiry=&limit=` – Search symbols by expiry
- `POST /api/symbols/refresh` – Refresh symbol master from FYERS CSVs
- `POST /api/clear_processor_state` – Clear per-symbol/timeframe processor cache when switching
   - body: `{ symbol, timeframe, bucket_size?, multiplier? }`
- `GET /api/historical?symbol=&timeframe=&bucket_size=&multiplier=` – Historical bars

## Socket.IO events
Namespace: default (`/socket.io/`). The Nginx proxy forwards upgrades, so the browser connects to `http://localhost/socket.io/`.

Client → Server
- `subscribe_symbol`
   - Expected payload:
      ```json
      {
         "symbol": "NSE:NIFTY25AUGFUT",
         "timeframe": "5m",
         "bucket_size": 0.05,
         "multiplier": 100,
         "chart_id": "chart-0",
         "hist_seed": { /* last historical candle to seed state */ }
      }
      ```
   - Joins an internal room keyed by client, chart, and parameters; starts global feed if needed; subscribes the symbol.

- `unsubscribe_symbol`
   - Expected payload:
      ```json
      { "symbol": "NSE:NIFTY25AUGFUT", "chart_id": "chart-0" }
      ```
   - Leaves the room; unsubscribes from the symbol when no subscribers remain.

Server → Client
- `connected` – On connect: `{ data: "Connected to TradeLab" }`
- `subscription_success` – Confirms subscription: `{ symbol, chart_id, room_id }`
- `unsubscription_success` – Confirms unsubscription: `{ symbol, chart_id }`
- `live_data_update` – Live ticks after processing/aggregation:
   - `{ symbol, chart_id, data, timeframe, timestamp }`
- `live_feed_error` – Feed error: `{ error }`
- `live_feed_closed` – Feed closed: `{ message }`
- `error` – Generic error: `{ message }`

Notes:
- The server maintains a single global FYERS WebSocket and multiplexes by rooms per subscription.
- Cumulative delta and other aggregates are computed in `core/fyers/processor.py`.

## Local development (without Docker)
1) Create a virtual environment and install dependencies
    - `python -m venv .venv`
    - `./.venv/Scripts/Activate.ps1` (PowerShell)
    - `pip install -r requirements.txt`

2) Configure env
    - `Copy-Item .env.example .env` and edit values.

3) Run the dev server
    - `python main.py`
    - Opens on `http://localhost:5000` by default.
    - For best Socket.IO performance locally, you can set `SOCKETIO_ASYNC_MODE=eventlet` and ensure `eventlet` is installed.

## Operations and logs
- App logs: `./logs/app.log` and `./logs/error.log`
- Nginx logs: `./logs/nginx/access.log`, `./logs/nginx/error.log`
- Healthcheck: Compose pings `http://app:8000/` (the container’s internal port) to mark the app healthy.

## Production hardening checklist
- Set a strong `SECRET_KEY`.
- Restrict `CORS_ORIGINS` to your exact site origins (avoid `*`).
- Put Nginx behind TLS (e.g., a load balancer, reverse proxy, or extend Compose with certbot/certs).
- Keep FYERS credentials and tokens secure (tokens persist at `data/token.json`).
- Monitor logs and consider rate limits/backoff for feed reconnects.

## Troubleshooting
WebSockets not connecting
- Symptom: 400 responses from `/socket.io/` or no live updates.
- Fixes: Ensure `eventlet` worker is used (Compose sets it), and set `CORS_ORIGINS` to include your site origin(s). Confirm Nginx config exists at `nginx/conf.d/app.conf` and Compose mounted it.

FYERS auth issues
- Ensure `FYERS_REDIRECT_URI` exactly matches the FYERS app config.
- For local Docker, use `http://localhost/fyers/callback`.
- Check `data/token.json` presence and app logs for token generation.

Import errors like `pkg_resources` missing
- Ensure `setuptools` is in `requirements.txt` (it is in this repo).

Windows + Docker Desktop specifics
- Use `docker compose` (not `docker-compose`). Ensure file sharing is enabled for the project drive.

Port conflicts
- Nginx binds to host port 80. If it’s in use, edit `docker-compose.yml` under `nginx.ports` (e.g., `"8080:80"`) and browse to that port.

If charts still don’t update
- Use the browser devtools Network tab to verify the Socket.IO connection upgrades to WebSocket.
- Watch app logs for lines like “Client connected”, “Started global live feed”, and “Global feed subscribed …”.
- Verify that `hist_seed` is sent from the frontend when subscribing; the processor will still work without it but seeding improves continuity.

---

Thanks for using TradeLab FPC. If you spot gaps or want to contribute, feel free to open a PR.