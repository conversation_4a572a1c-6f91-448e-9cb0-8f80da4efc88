"""Live tick and historical processing helpers for TradeLab.

This module provides:
- CandleAggregator: per-timeframe live tick to candle aggregator with footprint.
- TickToBucket5s: simple 5-second tick aggregator producing OHLCV buckets.
- process_hist_data: polars-based historical resampling with footprint building.
- process_live_data: backwards-compatible entry point used by the app.
- clear_processor_state: utility to clear in-memory aggregator state.

The implementation provides live tick aggregation and historical resampling
with proper cumulative delta calculation that resets at market open each day.
"""

import math
import polars as pl
from collections import deque, defaultdict
from decimal import Decimal, ROUND_FLOOR, InvalidOperation, getcontext
from typing import Dict, List, Tuple, Optional, Union, Iterable
from datetime import datetime, time, date

# Keep a small in-memory state to mirror previous behaviour
_candle_state: Dict[str, 'CandleAggregator'] = {}

# Shared interval map
_INTERVAL_MAP = {'1m': 60, '5m': 300, '15m': 900, '1d': 86400}

# Market hours configuration (IST)
MARKET_OPEN_HOUR = 9
MARKET_OPEN_MINUTE = 15
MARKET_CLOSE_HOUR = 15
MARKET_CLOSE_MINUTE = 30

# Decimal precision for historical operations
getcontext().prec = 16


# ----------------- Module-level helpers (shared) -----------------

def normalize_timestamp_to_seconds(raw_ts) -> Optional[int]:
    """Normalize timestamps in various units to integer seconds.

    Returns None for invalid inputs.
    """
    try:
        t = float(raw_ts)
    except Exception:
        return None
    if t <= 0:
        return None
    # heuristics: >1e17 -> ns, >1e14 -> us, >1e12 -> ms
    if t > 1e17:
        return int(t / 1e9)
    if t > 1e14:
        return int(t / 1e6)
    if t > 1e12:
        return int(t / 1e3)
    return int(t)


def calculate_aligned_time_bin(timestamp: int, interval_seconds: int) -> int:
    """Align timestamp to market open-based bins (India default 09:15 IST).

    If pytz is available, the function will try to respect timezone-aware
    timestamps; otherwise it falls back to naive datetime.
    """
    try:
        import pytz
        ist = pytz.timezone('Asia/Kolkata')
        dt = datetime.fromtimestamp(timestamp, tz=ist)
        use_tz = True
    except Exception:
        dt = datetime.fromtimestamp(timestamp)
        use_tz = False

    market_open_time = time(MARKET_OPEN_HOUR, MARKET_OPEN_MINUTE, 0)
    market_open_dt = datetime.combine(dt.date(), market_open_time)
    if use_tz and hasattr(dt, 'tzinfo') and dt.tzinfo:
        market_open_dt = market_open_dt.replace(tzinfo=dt.tzinfo)
    market_open_ts = int(market_open_dt.timestamp())

    if timestamp < market_open_ts:
        return (timestamp // interval_seconds) * interval_seconds
    seconds_since_open = timestamp - market_open_ts
    candle_period = seconds_since_open // interval_seconds
    return market_open_ts + (candle_period * interval_seconds)


def get_market_open_timestamp(dt: datetime) -> int:
    """Get the market open timestamp for a given datetime."""
    market_open = datetime.combine(dt.date(), time(MARKET_OPEN_HOUR, MARKET_OPEN_MINUTE, 0))
    try:
        import pytz
        ist = pytz.timezone('Asia/Kolkata')
        market_open = ist.localize(market_open)
    except Exception:
        pass
    return int(market_open.timestamp())


def is_same_trading_day(ts1: int, ts2: int) -> bool:
    """Check if two timestamps belong to the same trading day."""
    dt1 = datetime.fromtimestamp(ts1)
    dt2 = datetime.fromtimestamp(ts2)
    market_open1 = get_market_open_timestamp(dt1)
    market_open2 = get_market_open_timestamp(dt2)
    return market_open1 == market_open2


def get_bucket_key(price: float, bucket_size: float, multiplier: int, use_decimal: bool = True) -> float:
    """Return the price bucket (quantized) for a given price.

    Tries a fast cents-based path then falls back to Decimal for precision.
    """
    try:
        pv = float(price)
        bucket_value = float(bucket_size * multiplier)
        # fast cents path when bucket_value is a cent-multiple
        b_cents = int(round(bucket_value * 100)) if bucket_value > 0 else 0
        if b_cents > 0:
            int_price = int(round(pv * 100))
            bucket_index = int_price // b_cents
            bucket_price = (bucket_index * b_cents) / 100.0
            return round(bucket_price, 2)
    except Exception:
        pass

    if use_decimal:
        try:
            p = Decimal(str(price))
            unit = Decimal(str(bucket_size * multiplier))
            if unit == Decimal('0'):
                return float(round(price, 2))
            buckets = (p / unit).to_integral_value(rounding=ROUND_FLOOR)
            bucket_price = (buckets * unit).quantize(Decimal('0.01'))
            return float(bucket_price)
        except Exception:
            pass

    try:
        bucket_value = float(bucket_size * multiplier)
        return round(round(price / bucket_value) * bucket_value, 2)
    except Exception:
        return float(round(price, 2))


# ----------------- precise integer allocation helpers -----------------

def _proportional_alloc(total: int, weights: Iterable[int]) -> List[int]:
    """Allocate `total` as integers proportional to `weights` using the largest-remainder method.

    Returns a list of ints the same length as weights that sum to total.
    If all weights are zero or total <= 0, falls back to an even split (or zeros).
    """
    weights = [float(w) if w is not None else 0.0 for w in weights]
    n = len(weights)
    if n == 0:
        return []
    if total <= 0:
        return [0] * n
    s = sum(weights)
    if s <= 0:
        # evenly distribute remainder
        base = total // n
        res = [base] * n
        rem = total - base * n
        for i in range(rem):
            res[i] += 1
        return res

    exact = [total * (w / s) for w in weights]
    floored = [int(math.floor(x)) for x in exact]
    res = floored[:]
    rem = total - sum(res)
    if rem > 0:
        # distribute remaining by largest fractional parts
        fracs = sorted(range(n), key=lambda i: (exact[i] - floored[i]), reverse=True)
        for i in range(rem):
            res[fracs[i]] += 1
    return res


def _proportional_alloc_signed(delta: int, weights: Iterable[int]) -> List[int]:
    """Distribute a possibly negative integer `delta` proportionally across weights.

    Uses _proportional_alloc on abs(delta) and restores sign.
    """
    if delta == 0:
        return [0] * len(list(weights))
    sign = 1 if delta > 0 else -1
    alloc = _proportional_alloc(abs(int(delta)), weights)
    return [sign * a for a in alloc]


# ----------------- CandleAggregator (live per-tick) -----------------
class CandleAggregator:
    def __init__(self, timeframe: str, bucket_size: float, multiplier: int):
        self.timeframe = timeframe
        self.bucket_size = float(bucket_size)
        self.multiplier = int(multiplier)
        self._candles: Dict[str, Dict] = {}
        self._footprints: Dict[str, Dict[float, Dict[str, int]]] = {}
        self._recent_trades: Dict[str, deque] = {}
        self._last_ltp: Dict[str, float] = {}
        self._last_cum_volume: Dict[str, int] = {}
        self._last_processed_cum_volume: Dict[str, int] = {}
        
        # Cumulative delta tracking per symbol (resets each trading day)
        self._session_cum_delta: Dict[str, int] = {}
        self._last_trading_day_per_symbol: Dict[str, int] = {}
        self._current_candle_time: Dict[str, int] = {}

    def process_tick(self, msg: dict) -> Optional[Dict]:
        symbol = msg.get('symbol')
        ltp = msg.get('ltp')
        ts = msg.get('exch_feed_time') or msg.get('last_traded_time')
        if not symbol or ltp is None or ts is None:
            return None
        ts = normalize_timestamp_to_seconds(ts)
        if ts is None:
            return None
        try:
            ltp = float(ltp)
        except Exception:
            return None

        seconds = _INTERVAL_MAP.get(self.timeframe, 300)
        time_bin = calculate_aligned_time_bin(ts, seconds)

        vol = self._determine_trade_volume(symbol, msg)
        if vol <= 0 or vol > 5_000_000:
            return None

        if symbol not in self._recent_trades:
            self._recent_trades[symbol] = deque(maxlen=50)
        trade_key = (ts, ltp, self._last_processed_cum_volume.get(symbol, 0))
        if trade_key in self._recent_trades[symbol]:
            return None
        self._recent_trades[symbol].append(trade_key)

        last_ltp = self._last_ltp.get(symbol)
        self._last_ltp[symbol] = ltp

        buy, sell = self._calculate_buy_sell_volume(msg, vol)
        if buy + sell != vol:
            diff = vol - (buy + sell)
            if buy >= sell:
                buy += diff
            else:
                sell += diff

        bucket = get_bucket_key(ltp, self.bucket_size, self.multiplier)

        c = self._candles.get(symbol)
        is_new = (c is None or c.get('time') != time_bin)
        if is_new:
            # Check if we're starting a new candle - if so, finalize the previous candle's contribution
            prev_candle_time = self._current_candle_time.get(symbol)
            if prev_candle_time is not None and prev_candle_time != time_bin:
                # Previous candle is now complete, its delta is already in session_cum_delta
                pass
            
            # Check if this is a new trading day and reset cumulative delta if needed
            current_trading_day = get_market_open_timestamp(datetime.fromtimestamp(time_bin))
            last_trading_day = self._last_trading_day_per_symbol.get(symbol)
            
            if last_trading_day is None or current_trading_day != last_trading_day:
                # Reset cumulative delta for new trading day
                self._session_cum_delta[symbol] = 0
                self._last_trading_day_per_symbol[symbol] = current_trading_day
            
            # Track current candle time
            self._current_candle_time[symbol] = time_bin
            
            # Normal new-candle initialization
            # determine opening price: prefer provided daily open for first candle of day, else use ltp
            daily_open = msg.get('open_price')
            candle_open = daily_open if (daily_open is not None and self._is_first_candle_of_day(time_bin)) else ltp
            
            candle_delta = buy - sell
            
            # For a new candle, update session cumulative delta
            current_session_cum_delta = self._session_cum_delta.get(symbol, 0) + candle_delta
            self._session_cum_delta[symbol] = current_session_cum_delta
            
            self._candles[symbol] = {
                'time': time_bin,
                'open': candle_open,
                'high': ltp,
                'low': ltp,
                'close': ltp,
                'volume': vol,
                'buy_vol': buy,
                'sell_vol': sell,
                'delta': candle_delta,
                'cum_delta': current_session_cum_delta,
                'footprint': []
            }
            self._footprints[symbol] = {bucket: {'buy': buy, 'sell': sell}}
        else:
            c['high'] = max(c['high'], ltp)
            c['low'] = min(c['low'], ltp)
            c['close'] = ltp
            c['volume'] += vol
            c['buy_vol'] += buy
            c['sell_vol'] += sell
            
            # Calculate new delta for this candle
            old_delta = c.get('delta', 0)
            new_delta = int(c['buy_vol']) - int(c['sell_vol'])
            c['delta'] = new_delta
            
            # Update session cumulative delta by the change in this candle's delta
            delta_change = new_delta - old_delta
            current_session_cum_delta = self._session_cum_delta.get(symbol, 0) + delta_change
            self._session_cum_delta[symbol] = current_session_cum_delta
            c['cum_delta'] = current_session_cum_delta

            fp = self._footprints.setdefault(symbol, {})
            if bucket not in fp:
                fp[bucket] = {'buy': 0, 'sell': 0}
            fp[bucket]['buy'] += buy
            fp[bucket]['sell'] += sell

        # Reconcile totals and footprint
        self._reconcile_candle_and_footprint(symbol)

        return self._candles[symbol].copy()

    def _determine_trade_volume(self, symbol: str, msg: dict) -> int:
        raw_trade = msg.get('last_traded_qty')
        cum = msg.get('vol_traded_today')
        if isinstance(cum, (int, float)) and cum >= 0:
            self._last_cum_volume[symbol] = int(cum)
        if isinstance(cum, (int, float)) and cum >= 0:
            cur = int(cum)
            last = self._last_processed_cum_volume.get(symbol)
            if last is None:
                self._last_processed_cum_volume[symbol] = cur
                return int(raw_trade) if isinstance(raw_trade, (int, float)) and raw_trade > 0 else 0
            if cur < last:
                self._last_processed_cum_volume[symbol] = cur
                return int(raw_trade) if isinstance(raw_trade, (int, float)) and raw_trade > 0 else 0
            delta = cur - last
            if delta <= 0 or delta > 2_000_000:
                return 0
            self._last_processed_cum_volume[symbol] = cur
            return int(delta)
        if isinstance(raw_trade, (int, float)) and raw_trade > 0:
            return int(raw_trade)
        return 0

    def _calculate_buy_sell_volume(self, msg: dict, vol: int) -> Tuple[int, int]:
        if vol <= 0:
            return 0, 0
        ltp = msg.get('ltp')
        bid = msg.get('bid_price')
        ask = msg.get('ask_price')
        tot_buy_qty = msg.get('tot_buy_qty')
        tot_sell_qty = msg.get('tot_sell_qty')
        eps = 1e-6
        buy = sell = 0

        if bid is not None and ask is not None and ltp is not None:
            if ask >= bid:
                if ltp >= ask - eps:
                    buy = vol
                elif ltp <= bid + eps:
                    sell = vol
                else:
                    if isinstance(tot_buy_qty, (int, float)) and isinstance(tot_sell_qty, (int, float)) and (tot_buy_qty + tot_sell_qty) > 0:
                        total_pressure = tot_buy_qty + tot_sell_qty
                        buy = int(round(vol * (tot_buy_qty / total_pressure)))
                        sell = vol - buy
                    else:
                        buy = vol // 2
                        sell = vol - buy
            else:
                if isinstance(tot_buy_qty, (int, float)) and isinstance(tot_sell_qty, (int, float)) and (tot_buy_qty + tot_sell_qty) > 0:
                    total_pressure = tot_buy_qty + tot_sell_qty
                    buy = int(round(vol * (tot_buy_qty / total_pressure)))
                    sell = vol - buy
        if buy + sell == 0 and isinstance(tot_buy_qty, (int, float)) and isinstance(tot_sell_qty, (int, float)) and (tot_buy_qty + tot_sell_qty) > 0:
            total_pressure = tot_buy_qty + tot_sell_qty
            buy = int(round(vol * (tot_buy_qty / total_pressure)))
            sell = vol - buy
        if buy + sell == 0:
            ch = msg.get('ch')
            if isinstance(ch, (int, float)):
                if ch > 0:
                    buy = vol
                elif ch < 0:
                    sell = vol
                else:
                    buy = vol // 2
                    sell = vol - buy
            else:
                buy = vol // 2
                sell = vol - buy
        if buy + sell != vol:
            rem = vol - (buy + sell)
            if rem != 0:
                if buy >= sell:
                    buy += rem
                else:
                    sell += rem
        return int(buy), int(sell)

    def _reconcile_candle_and_footprint(self, symbol: str):
        c = self._candles.get(symbol)
        if not c:
            return
        fp_map = self._footprints.setdefault(symbol, {})
        try:
            vol = int(c.get('volume', 0))
            buy = int(c.get('buy_vol', 0))
            sell = int(c.get('sell_vol', 0))
        except Exception:
            return

        diff = vol - (buy + sell)
        if diff != 0:
            if diff < 0:
                total = buy + sell
                if total > 0:
                    # proportional integer allocation to avoid rounding drift
                    a_buy, a_sell = _proportional_alloc(int(vol), [buy, sell])
                    buy = max(0, a_buy)
                    sell = max(0, a_sell)
                else:
                    buy = 0
                    sell = 0
            else:
                if buy == 0 and sell == 0:
                    o = c.get('open') or 0
                    cl = c.get('close') or 0
                    if cl > o:
                        buy += diff
                    elif cl < o:
                        sell += diff
                    else:
                        half = diff // 2
                        buy += half
                        sell += diff - half
                else:
                    total = buy + sell
                    if total > 0:
                        add_buy, add_sell = _proportional_alloc(int(diff), [buy, sell])
                        buy += add_buy
                        sell += add_sell
                    else:
                        half = diff // 2
                        buy += half
                        sell += diff - half
        c['buy_vol'] = int(buy)
        c['sell_vol'] = int(sell)
        old_delta = c.get('delta', 0)
        new_delta = int(c['buy_vol']) - int(c['sell_vol'])
        c['delta'] = new_delta
        
        # Update session cumulative delta by the change in this candle's delta
        delta_change = new_delta - old_delta
        current_session_cum_delta = self._session_cum_delta.get(symbol, 0) + delta_change
        self._session_cum_delta[symbol] = current_session_cum_delta
        c['cum_delta'] = current_session_cum_delta

        # reconcile footprint buckets
        try:
            cur_buy = sum(int(v.get('buy', 0)) for v in fp_map.values())
            cur_sell = sum(int(v.get('sell', 0)) for v in fp_map.values())
        except Exception:
            cur_buy = cur_sell = 0

        tgt_buy = int(c.get('buy_vol', 0))
        tgt_sell = int(c.get('sell_vol', 0))
        d_buy = tgt_buy - cur_buy
        d_sell = tgt_sell - cur_sell

        if (d_buy != 0 or d_sell != 0):
            total_bucket = cur_buy + cur_sell
            if total_bucket > 0:
                # compute proportional adjustments for each bucket preserving integer totals
                keys = list(fp_map.keys())
                weights_total = [int(fp_map[k].get('buy', 0)) + int(fp_map[k].get('sell', 0)) for k in keys]
                # distribute d_buy and d_sell separately using weights_total but ensuring sums match
                add_buys = _proportional_alloc(int(d_buy), weights_total) if d_buy != 0 else [0] * len(keys)
                add_sells = _proportional_alloc(int(d_sell), weights_total) if d_sell != 0 else [0] * len(keys)
                for idx, k in enumerate(keys):
                    v = fp_map[k]
                    v['buy'] = max(0, int(v.get('buy', 0)) + add_buys[idx])
                    v['sell'] = max(0, int(v.get('sell', 0)) + add_sells[idx])
            else:
                try:
                    poc_bucket = get_bucket_key(c.get('close', 0), self.bucket_size, self.multiplier)
                except Exception:
                    poc_bucket = round(c.get('close', 0), 2)
                fp_map.setdefault(poc_bucket, {'buy': 0, 'sell': 0})
                fp_map[poc_bucket]['buy'] = int(tgt_buy)
                fp_map[poc_bucket]['sell'] = int(tgt_sell)

        c['footprint'] = build_footprint_from_map(c, fp_map, self.bucket_size * self.multiplier)

    def _is_first_candle_of_day(self, time_bin: int) -> bool:
        dt = datetime.fromtimestamp(time_bin)
        if self.timeframe in ['1m', '5m', '15m']:
            return dt.hour == MARKET_OPEN_HOUR and dt.minute == MARKET_OPEN_MINUTE
        return dt.hour == MARKET_OPEN_HOUR and dt.minute <= MARKET_OPEN_MINUTE + 5


# ----------------- 5s Tick Aggregator -----------------
class TickToBucket5s:
    """Aggregates ticks into 5-second buckets. Outputs rows compatible with process_hist_data input."""
    def __init__(self, bucket_size: float = 0.05, multiplier: int = 100):
        self.bucket_size = float(bucket_size)
        self.multiplier = int(multiplier)
        self._buckets: Dict[Tuple[int, str], Dict] = {}
        self._recent_trades: Dict[str, deque] = defaultdict(lambda: deque(maxlen=200))
        self._last_processed_cum_volume: Dict[str, int] = {}

    def process_tick(self, msg: dict) -> Optional[Dict]:
        symbol = msg.get('symbol')
        ltp = msg.get('ltp')
        ts = msg.get('exch_feed_time') or msg.get('last_traded_time')
        if not symbol or ltp is None or ts is None:
            return None
        ts = normalize_timestamp_to_seconds(ts)
        if ts is None:
            return None
        try:
            ltp = float(ltp)
        except Exception:
            return None

        # 5-second bin
        time_bin = calculate_aligned_time_bin(ts, 5)

        # determine volume using same logic as CandleAggregator
        vol = self._determine_trade_volume(symbol, msg)
        if vol <= 0 or vol > 5_000_000:
            return None

        trade_key = (ts, ltp, self._last_processed_cum_volume.get(symbol, 0))
        if trade_key in self._recent_trades[symbol]:
            return None
        self._recent_trades[symbol].append(trade_key)

        # allocate buy/sell using same aggressor logic
        buy, sell = CandleAggregator('1m', self.bucket_size, self.multiplier)._calculate_buy_sell_volume(msg, vol)

        key = (time_bin, symbol)
        b = self._buckets.get(key)
        if b is None:
            b = {
                'timestamp': time_bin,
                'symbol': symbol,
                'open': ltp,
                'high': ltp,
                'low': ltp,
                'close': ltp,
                'volume': vol,
                'buy_vol': buy,
                'sell_vol': sell,
            }
            self._buckets[key] = b
        else:
            b['high'] = max(b['high'], ltp)
            b['low'] = min(b['low'], ltp)
            b['close'] = ltp
            b['volume'] += vol
            b['buy_vol'] += buy
            b['sell_vol'] += sell

        # return completed bucket only when time moves beyond it (caller must manage lifecycle)
        return b.copy()

    def _determine_trade_volume(self, symbol: str, msg: dict) -> int:
        raw_trade = msg.get('last_traded_qty')
        cum = msg.get('vol_traded_today')
        if isinstance(cum, (int, float)) and cum >= 0:
            self._last_processed_cum_volume.setdefault(symbol, int(cum))
        if isinstance(cum, (int, float)) and cum >= 0:
            cur = int(cum)
            last = self._last_processed_cum_volume.get(symbol)
            if last is None:
                self._last_processed_cum_volume[symbol] = cur
                return int(raw_trade) if isinstance(raw_trade, (int, float)) and raw_trade > 0 else 0
            if cur < last:
                self._last_processed_cum_volume[symbol] = cur
                return int(raw_trade) if isinstance(raw_trade, (int, float)) and raw_trade > 0 else 0
            delta = cur - last
            if delta <= 0 or delta > 2_000_000:
                return 0
            self._last_processed_cum_volume[symbol] = cur
            return int(delta)
        if isinstance(raw_trade, (int, float)) and raw_trade > 0:
            return int(raw_trade)
        return 0

    def flush(self) -> List[Dict]:
        # Return all current buckets as a list and clear
        rows = list(self._buckets.values())
        self._buckets.clear()
        return rows


# ----------------- Historical processing (Polars-based) -----------------
def process_hist_data(df: pl.DataFrame, timeframe: str, symbol_col: Optional[str] = None,
                      data_frame: bool = False, footprint: bool = True,
                      bucket_size: float = 0.05, multiplier: int = 100, preserve_live_data: bool = True):
    if not isinstance(df, pl.DataFrame):
        raise ValueError('Input must be a Polars DataFrame')
    if 'timestamp' not in df.columns:
        raise ValueError('DataFrame must have a timestamp column')

    seconds = _INTERVAL_MAP.get(timeframe, 300)

    # If df already contains buy_vol/sell_vol we should respect them; otherwise compute heuristics
    has_precomputed = 'buy_vol' in df.columns and 'sell_vol' in df.columns
    
    # When preserve_live_data is True and we have precomputed buy/sell volumes, 
    # don't override them with heuristics
    use_precomputed = has_precomputed and preserve_live_data

    duplicate_cols = ['timestamp']
    group_cols = ['time']
    if symbol_col and symbol_col in df.columns:
        duplicate_cols.append(symbol_col)
        group_cols.append(symbol_col)

    # Historically we used heuristic buy/sell. If precomputed provided, skip generating them.
    buy_expr = (
        pl.when(pl.col('close') > pl.col('open')).then(pl.col('volume'))
          .when((pl.col('close') == pl.col('open')) & (pl.col('close') > pl.col('close').shift(1)))
          .then(pl.col('volume'))
          .otherwise(0)
    )
    sell_expr = (
        pl.when(pl.col('close') < pl.col('open')).then(pl.col('volume'))
          .when((pl.col('close') == pl.col('open')) & (pl.col('close') < pl.col('close').shift(1)))
          .then(pl.col('volume'))
          .otherwise(0)
    )

    ldf = df.lazy().with_columns([
        pl.col(symbol_col).cast(pl.Categorical) if symbol_col and symbol_col in df.columns else pl.lit(None),
        pl.col('timestamp').cast(pl.Int64)
    ])

    ldf = ldf.unique(subset=duplicate_cols, keep='first').with_columns([(pl.col('timestamp') // seconds * seconds).alias('time')])
    if symbol_col and symbol_col in df.columns:
        ldf = ldf.sort([symbol_col, 'timestamp'])
    else:
        ldf = ldf.sort('timestamp')

    if not use_precomputed:
        ldf = ldf.with_columns([buy_expr.alias('buy_vol'), sell_expr.alias('sell_vol')])

    agg = [
        pl.col('open').first().alias('open'),
        pl.col('high').max().alias('high'),
        pl.col('low').min().alias('low'),
        pl.col('close').last().alias('close'),
        pl.col('volume').sum().alias('volume'),
        pl.col('buy_vol').sum().alias('buy_vol'),
        pl.col('sell_vol').sum().alias('sell_vol')
    ]

    grouped = ldf.group_by(group_cols).agg(agg).with_columns([(pl.col('buy_vol') - pl.col('sell_vol')).alias('delta')])

    out_cols = group_cols + ['open', 'high', 'low', 'close', 'volume', 'buy_vol', 'sell_vol', 'delta']
    output_df = grouped.select(out_cols).collect().sort('time').unique(subset=['time'], keep='first')

    out_rows = output_df.to_dicts()

    # Reconcile buy/sell totals
    for r in out_rows:
        try:
            vol = int(r.get('volume') or 0)
            buy = int(r.get('buy_vol') or 0)
            sell = int(r.get('sell_vol') or 0)
        except Exception:
            continue
        diff = vol - (buy + sell)
        if diff == 0:
            r['delta'] = r.get('delta', buy - sell)
            continue
        if diff < 0:
            total = buy + sell
            if total > 0:
                new_buy = int(round(buy * (vol / total)))
                new_sell = vol - new_buy
                r['buy_vol'] = max(0, new_buy)
                r['sell_vol'] = max(0, new_sell)
            else:
                r['buy_vol'] = 0
                r['sell_vol'] = 0
        else:
            if buy == 0 and sell == 0:
                o = r.get('open') or 0
                c = r.get('close') or 0
                if c > o:
                    r['buy_vol'] = buy + diff
                    r['sell_vol'] = sell
                elif c < o:
                    r['sell_vol'] = sell + diff
                    r['buy_vol'] = buy
                else:
                    half = diff // 2
                    r['buy_vol'] = buy + half
                    r['sell_vol'] = sell + (diff - half)
            else:
                total = buy + sell
                if total > 0:
                    add_buy = int(round(diff * (buy / total)))
                    add_sell = diff - add_buy
                    r['buy_vol'] = buy + add_buy
                    r['sell_vol'] = sell + add_sell
                else:
                    half = diff // 2
                    r['buy_vol'] = buy + half
                    r['sell_vol'] = sell + (diff - half)
        try:
            r['delta'] = int(r.get('buy_vol', 0)) - int(r.get('sell_vol', 0))
        except Exception:
            pass

    # --- compute cumulative delta per symbol per trading day ---
    try:
        grouped_rows = {}
        for r in out_rows:
            sym_key = r.get(symbol_col) if (symbol_col and symbol_col in r) else None
            grouped_rows.setdefault(sym_key, []).append(r)

        for sym_key, rows in grouped_rows.items():
            rows.sort(key=lambda x: int(x.get('time', 0)))
            cum = 0
            current_trading_day = None
            for r in rows:
                try:
                    candle_time = int(r.get('time', 0))
                    dt = datetime.fromtimestamp(candle_time)
                    trading_day = get_market_open_timestamp(dt)
                except Exception:
                    trading_day = None

                # Reset cumulative delta at the start of a new trading day
                if current_trading_day is None or trading_day != current_trading_day:
                    cum = 0
                    current_trading_day = trading_day

                try:
                    delta_val = int(r.get('delta', 0))
                except Exception:
                    delta_val = 0

                cum += delta_val
                r['cum_delta'] = int(cum)
    except Exception as e:
        print(f"Error calculating cumulative delta in historical data: {e}")
        # best-effort: leave rows unchanged
        pass

    # Footprint calculation
    if footprint:
        bucket = bucket_size * multiplier
        if bucket <= 0:
            if data_frame:
                import pandas as pd
                return pd.DataFrame(out_rows)
            return clean_nans(out_rows)

        # Use consistent bucket calculation with live processing
        def calc_bucket_key(close_prices):
            return [get_bucket_key(price, bucket_size, multiplier) for price in close_prices]
        
        df_exp = (
            df.lazy()
              .with_columns([
                  ((pl.col('timestamp') // seconds) * seconds).alias('time'),
                  (pl.col('buy_vol') if 'buy_vol' in df.columns else buy_expr).alias('buy_vol'),
                  (pl.col('sell_vol') if 'sell_vol' in df.columns else sell_expr).alias('sell_vol')
              ])
              .collect()
              .with_columns([
                  pl.col('close').map_elements(lambda x: get_bucket_key(x, bucket_size, multiplier), return_dtype=pl.Float64).alias('price_bucket')
              ])
        )

        fp_group_cols = ['time', 'price_bucket']
        if symbol_col and symbol_col in df.columns:
            fp_group_cols.append(symbol_col)

        fp_df = (
            df_exp.group_by(fp_group_cols)
                  .agg([
                      pl.col('buy_vol').sum().alias('buyVolume'),
                      pl.col('sell_vol').sum().alias('sellVolume')
                  ])
                  .sort(['time', 'price_bucket'])
        )

        fp_dict = {}
        sym_present = symbol_col and symbol_col in fp_df.columns
        for row in fp_df.iter_rows(named=True):
            key = (row['time'], row[symbol_col]) if sym_present else (row['time'],)
            fp_dict.setdefault(key, {})[float(row['price_bucket'])] = {
                'buyVolume': int(row['buyVolume']),
                'sellVolume': int(row['sellVolume'])
            }

        for r in out_rows:
            key = (r['time'], r.get(symbol_col)) if (symbol_col and symbol_col in r) else (r['time'],)
            
            # Use the same footprint building logic as live processing
            fp_data = fp_dict.get(key, {})
            
            # Convert historical footprint data to the internal map format
            fp_map = {}
            for price_level, data in fp_data.items():
                fp_map[float(price_level)] = {
                    'buy': int(data.get('buyVolume', 0)),
                    'sell': int(data.get('sellVolume', 0))
                }
            
            # Use the same footprint building function as live processing
            bucket_value = bucket_size * multiplier
            r['footprint'] = build_footprint_from_map(r, fp_map, bucket_value)

        if data_frame:
            import pandas as pd
            return pd.DataFrame(out_rows)
        return clean_nans(out_rows)

    if data_frame:
        import pandas as pd
        return pd.DataFrame(out_rows)
    return clean_nans(out_rows)


# ----------------- helpers for footprint building -----------------

def build_footprint_from_map(candle: Dict, fp_map: Dict[float, Dict[str, int]], bucket_value: float) -> List[Dict]:
    if bucket_value <= 0 or not fp_map:
        return []
    low = candle['low']
    high = candle['high']
    try:
        min_bucket = math.floor(low / bucket_value) * bucket_value
        max_bucket = math.floor(high / bucket_value) * bucket_value
    except Exception:
        return []

    ladder = []
    try:
        current = Decimal(str(min_bucket))
        step = Decimal(str(bucket_value))
        max_dec = Decimal(str(max_bucket))
        iters = 0
        max_iters = 5000
        while current <= max_dec and iters < max_iters:
            level = float(round(current, 2))
            entry = fp_map.get(level, {'buy': 0, 'sell': 0})
            ladder.append({'priceLevel': level, 'buyVolume': int(entry.get('buy', 0)), 'sellVolume': int(entry.get('sell', 0))})
            current += step
            iters += 1
    except Exception:
        try:
            current = min_bucket
            iters = 0
            while current <= max_bucket + 1e-9 and iters < 5000:
                level = round(current, 2)
                entry = fp_map.get(level, {'buy': 0, 'sell': 0})
                ladder.append({'priceLevel': level, 'buyVolume': int(entry.get('buy', 0)), 'sellVolume': int(entry.get('sell', 0))})
                current = round(current + bucket_value, 6)
                iters += 1
        except Exception:
            return []

    ladder = sorted(ladder, key=lambda x: x['priceLevel'], reverse=True)

    try:
        cur_buy = sum(int(item.get('buyVolume', 0)) for item in ladder)
        cur_sell = sum(int(item.get('sellVolume', 0)) for item in ladder)
        tgt_buy = int(candle.get('buy_vol', 0))
        tgt_sell = int(candle.get('sell_vol', 0))
        d_buy = tgt_buy - cur_buy
        d_sell = tgt_sell - cur_sell
        if d_buy != 0 or d_sell != 0:
                total_volume = cur_buy + cur_sell
                if total_volume > 0:
                    weights = [item['buyVolume'] + item['sellVolume'] for item in ladder]
                    add_buys = _proportional_alloc(int(d_buy), weights) if d_buy != 0 else [0] * len(ladder)
                    add_sells = _proportional_alloc(int(d_sell), weights) if d_sell != 0 else [0] * len(ladder)
                    for idx, item in enumerate(ladder):
                        item['buyVolume'] = max(0, int(item['buyVolume'] + add_buys[idx]))
                        item['sellVolume'] = max(0, int(item['sellVolume'] + add_sells[idx]))
        cur_buy = sum(int(item.get('buyVolume', 0)) for item in ladder)
        cur_sell = sum(int(item.get('sellVolume', 0)) for item in ladder)
        if ladder and (cur_buy != tgt_buy or cur_sell != tgt_sell):
            largest_idx = max(range(len(ladder)), key=lambda i: ladder[i]['buyVolume'] + ladder[i]['sellVolume'])
            ladder[largest_idx]['buyVolume'] = max(0, int(ladder[largest_idx]['buyVolume'] + (tgt_buy - cur_buy)))
            ladder[largest_idx]['sellVolume'] = max(0, int(ladder[largest_idx]['sellVolume'] + (tgt_sell - cur_sell)))
    except Exception:
        pass

    return ladder


# ----------------- utility -----------------

def clean_nans(obj):
    if isinstance(obj, float):
        if math.isnan(obj) or math.isinf(obj):
            return None
        return obj
    if obj is None:
        return None
    if isinstance(obj, dict):
        return {k: clean_nans(v) for k, v in obj.items()}
    if isinstance(obj, list):
        return [clean_nans(x) for x in obj]
    return obj


# ----------------- Backwards compatible APIs -----------------

def process_live_data(msg, timeframe, bucket_size, multiplier, hist_last_candle: Optional[Dict] = None):
    """Process a live tick message.    
    """
    try:
        if not isinstance(msg, dict) or not msg.get('symbol') or not msg.get('ltp'):
            return None
        bucket_size = float(bucket_size)
        multiplier = int(multiplier)
        symbol = msg.get('symbol')
        key = f"{symbol}_{timeframe}_{bucket_size}_{multiplier}"
        # create or re-use aggregator for this symbol/timeframe
        if key not in _candle_state:
            _candle_state[key] = CandleAggregator(timeframe, bucket_size, multiplier)
        agg = _candle_state[key]

        # If caller provided the last historical candle, seed aggregator state so
        # live ticks continue from that candle rather than starting a fresh one.
        try:
            if isinstance(hist_last_candle, dict):
                h = hist_last_candle
                # ensure we have a time field
                h_time = int(h.get('time') or h.get('timestamp') or 0)
                if h_time > 0:
                    cur_c = agg._candles.get(symbol)
                    # seed only when aggregator has no candle or has an older/different time
                    need_seed = (cur_c is None) or (int(cur_c.get('time', 0)) != int(h_time))
                    if need_seed:
                        # normalize numeric fields and copy
                        seeded = {
                            'time': int(h_time),
                            'open': float(h.get('open', 0.0) or 0.0),
                            'high': float(h.get('high', h.get('close', 0.0) or 0.0)),
                            'low': float(h.get('low', h.get('close', 0.0) or 0.0)),
                            'close': float(h.get('close', 0.0) or 0.0),
                            'volume': int(h.get('volume', 0) or 0),
                            'buy_vol': int(h.get('buy_vol', 0) or 0),
                            'sell_vol': int(h.get('sell_vol', 0) or 0),
                            'delta': int(h.get('delta', 0) or 0),
                            'cum_delta': int(h.get('cum_delta', 0) or 0),
                            'footprint': h.get('footprint', []) or []
                        }
                        agg._candles[symbol] = seeded
                        
                        # Initialize session cumulative delta tracking for this symbol
                        agg._session_cum_delta[symbol] = int(h.get('cum_delta', 0) or 0)
                        agg._current_candle_time[symbol] = h_time
                        
                        # Set the trading day for this symbol
                        try:
                            trading_day = get_market_open_timestamp(datetime.fromtimestamp(h_time))
                            agg._last_trading_day_per_symbol[symbol] = trading_day
                        except Exception:
                            pass

                        # convert footprint list (if present) into internal map format
                        fp_map = {}
                        try:
                            fp_list = h.get('footprint') or []
                            for item in fp_list:
                                # historical footprint may use different keys
                                price = item.get('priceLevel') if 'priceLevel' in item else item.get('price') if 'price' in item else None
                                buyv = item.get('buyVolume') if 'buyVolume' in item else item.get('buy', 0)
                                sellv = item.get('sellVolume') if 'sellVolume' in item else item.get('sell', 0)
                                if price is None:
                                    continue
                                try:
                                    p = float(price)
                                except Exception:
                                    continue
                                fp_map[round(p, 2)] = {'buy': int(buyv or 0), 'sell': int(sellv or 0)}
                        except Exception:
                            fp_map = {}
                        if fp_map:
                            agg._footprints[symbol] = fp_map

                        # set last ltp to close to help aggressor logic for first live tick
                        try:
                            agg._last_ltp[symbol] = float(seeded.get('close', 0.0) or 0.0)
                        except Exception:
                            pass
        except Exception:
            # best-effort seeding; ignore on failure
            pass

        # If caller passed a harmless seeding tick (common from callers that
        # attempt to seed state), don't process that tick as a real trade.
        # Typical seed ticks contain zero `last_traded_qty` and set
        # `vol_traded_today` to the historical candle's per-interval volume
        # (not the exchange cumulative). Processing such a tick will corrupt
        # `_last_processed_cum_volume` and can cause the first live update to
        # incorrectly adjust historical volume. Detect this pattern and return
        # the seeded candle instead of feeding it through `process_tick`.
        try:
            seeded_c = agg._candles.get(symbol)
            if isinstance(seeded_c, dict):
                seed_last_qty = msg.get('last_traded_qty')
                seed_cum = msg.get('vol_traded_today')
                if ( (seed_last_qty is None or int(seed_last_qty or 0) == 0)
                     and isinstance(seed_cum, (int, float))
                     and int(seed_cum) == int(seeded_c.get('volume', 0)) ):
                    # Return copy of seeded candle and avoid touching
                    # _last_processed_cum_volume via process_tick.
                    return seeded_c.copy()
        except Exception:
            # If detection fails, fall back to normal processing.
            pass

        return agg.process_tick(msg)
    except Exception:
        return None


def clear_processor_state(symbol, timeframe, bucket_size, multiplier):
    key = f"{symbol}_{timeframe}_{bucket_size}_{multiplier}"
    if key in _candle_state:
        del _candle_state[key]
        return True
    return False
