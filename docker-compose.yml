services:
  app:
    build: .
    env_file:
      - .env
    environment:
      FLASK_ENV: production
      SOCKETIO_ASYNC_MODE: eventlet
      CORS_ORIGINS: "*"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    expose:
      - "8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 5s
      retries: 3

  nginx:
    image: nginx:1.25-alpine
    depends_on:
      - app
    ports:
      - "80:80"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./static:/var/www/static:ro
      - ./logs/nginx:/var/log/nginx