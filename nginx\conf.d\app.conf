server {
    listen 80;
    server_name _;

    # Serve static files directly
    location /static/ {
        alias /var/www/static/;
        access_log off;
        expires 7d;
    }

    # WebSocket (Socket.IO) upgrade path
    location /socket.io/ {
        proxy_pass         http://app:8000/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header   Upgrade $http_upgrade;
        proxy_set_header   Connection "upgrade";
        proxy_set_header   Host $host;
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
    }

    # Default: proxy to app
    location / {
        proxy_pass         http://app:8000/;
        proxy_http_version 1.1;
        proxy_set_header   Host $host;
        proxy_set_header   X-Forwarded-Proto $scheme;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
    }
}