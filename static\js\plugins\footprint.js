// --- High Performance Footprint Chart Plugin ---
import { positionsLine, positionsBox, drawRounded<PERSON>andle, isValidOHLCData } from './chart-utils.js';

/**
 * High-performance footprint renderer with optimized drawing and text caching
 */
class FootprintRenderer {
    constructor() {
        this._data = null;
        this._options = null;
        this._textCache = new Map();
        this._font10 = '10px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
        this._font9 = '9px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
        this._maxCacheSize = 200; // Increased cache size for better performance
        
        // COT calculation cache - recalculated when data changes
        this._cotCache = new Map();
        this._lastVisibleRange = null;
    }

    draw(target, priceToCoordinate) {
        target.useBitmapCoordinateSpace(scope => this._drawImpl(scope, priceToCoordinate));
    }

    update(data, options) {
        this._data = data;
        this._options = options;
        
        // Manage cache size for memory efficiency - only clear when cache gets too large
        // to prevent unnecessary clearing that causes blinking
        if (this._textCache.size > this._maxCacheSize) {
            // Clear only half the cache instead of clearing everything
            const entries = Array.from(this._textCache.entries());
            const toKeep = entries.slice(-Math.floor(this._maxCacheSize / 2));
            this._textCache.clear();
            toKeep.forEach(([key, value]) => this._textCache.set(key, value));
        }
    }
    _drawImpl(scope, priceToCoordinate) {
        const d = this._data;
        if (!d?.bars?.length || !d.visibleRange || !this._options) return;
        
        const ctx = scope.context;
        const { from, to } = d.visibleRange;
        const barSpacing = d.barSpacing;
        
        // Use simple candlesticks for very small spacing
        if (barSpacing < 6) {
            this._drawCandles(ctx, priceToCoordinate, scope, from, to);
            return;
        }
        
        ctx.save();
        ctx.imageSmoothingEnabled = false;
        
        try {
            for (let i = from; i < to; ++i) {
                const bar = d.bars[i];
                if (bar?.originalData && isValidOHLCData(bar.originalData)) {
                    this._drawBar(ctx, bar, priceToCoordinate, scope, barSpacing, i);
                }
            }
        } finally {
            ctx.restore();
        }
    }

    _drawCandles(ctx, priceToCoordinate, scope, from, to) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        for (let i = from; i < to; ++i) {
            const bar = this._data.bars[i];
            if (!bar?.originalData || !isValidOHLCData(bar.originalData)) continue;
            
            const d = bar.originalData;
            const x = bar.x;
            const openY = priceToCoordinate(d.open);
            const closeY = priceToCoordinate(d.close);
            const highY = priceToCoordinate(d.high);
            const lowY = priceToCoordinate(d.low);
            
            if ([openY, closeY, highY, lowY].some(Number.isNaN)) continue;
            
            const isUp = d.close >= d.open;
            ctx.fillStyle = isUp ? this._options.upColor : this._options.downColor;
            ctx.fillRect(Math.round(x * h), Math.min(openY, closeY) * v, 1, Math.abs(closeY - openY) * v || 1);
            
            if (this._options.wickVisible) {
                ctx.fillStyle = isUp ? this._options.wickUpColor : this._options.wickDownColor;
                ctx.fillRect(Math.round(x * h), Math.min(highY, lowY) * v, 1, Math.abs(lowY - highY) * v || 1);
            }
        }
    }

    _drawBar(ctx, bar, priceToCoordinate, scope, barSpacing, barIndex) {
        const { originalData: d, x } = bar;
        const bodyW = Math.max(2, Math.min(12, barSpacing * 0.8));
        
        this._drawCandle(ctx, d, x, bodyW, priceToCoordinate, scope);
        
        if (barSpacing >= 18 && Array.isArray(d.footprint) && d.footprint.length) {
            const fpW = this._footprintWidth(ctx, d.footprint);
            this._drawCells(ctx, d, x + bodyW / 2 + 4, fpW, priceToCoordinate, scope, barIndex);
        }
    }

    _drawCandle(ctx, d, x, w, priceToCoordinate, scope) {
        drawRoundedCandle(ctx, d, x, w, priceToCoordinate, scope, this._options);
    }

    _footprintWidth(ctx, footprint) {
        const key = footprint.map(f => `${f.buyVolume}x${f.sellVolume}`).join('|');
        if (this._textCache.has(key)) return this._textCache.get(key);
        
        ctx.font = this._font10;
        let maxW = 0;
        
        for (const f of footprint) {
            const txt = this._formatText(f.buyVolume, f.sellVolume, f.buyVolume - f.sellVolume);
            maxW = Math.max(maxW, ctx.measureText(txt).width);
        }
        
        const fpW = Math.max(36, Math.ceil(maxW) + 10);
        this._textCache.set(key, fpW);
        return fpW;
    }

    _drawCells(ctx, d, startX, width, priceToCoordinate, scope, barIndex) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const fp = Array.isArray(d.footprint) ? [...d.footprint].sort((a, b) => b.priceLevel - a.priceLevel) : [];
        if (!fp.length) return;

        ctx.font = this._font9;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Gradient color stops
        const downGradient = this._options.downGradientColors || [
            '#F08090', '#D94A5A', '#A12A3A', '#3B0D16'
        ];
        const upGradient = this._options.upGradientColors || [
            '#5FE1C5', '#2FC1A2', '#007C65', '#00332A'
        ];
        const neutralColor = this._options.neutralColor || '#B2B5BE';

        const volumes = fp.map(f => (f.buyVolume || 0) + (f.sellVolume || 0));
        const maxVol = Math.max(...volumes);
        const minVol = Math.min(...volumes);
        const volRange = maxVol - minVol || 1;

    const cellGeometry = new Map();
        const xPosStatic = positionsLine(startX + width / 2, h, width);

        // Calculate cell boundaries for each price level to fill gaps equally
        for (let i = 0; i < fp.length; ++i) {
            const f = fp[i];
            const currentPrice = f.priceLevel;
            
            // Calculate cell boundaries by finding midpoints between adjacent price levels
            let cellTop, cellBottom;
            
            if (i === 0) {
                // Top cell: extend from current price to midpoint with next price level
                const nextPrice = fp[i + 1]?.priceLevel;
                if (nextPrice !== undefined) {
                    const midPrice = (currentPrice + nextPrice) / 2;
                    cellTop = priceToCoordinate(currentPrice + (currentPrice - midPrice));
                    cellBottom = priceToCoordinate(midPrice);
                } else {
                    // Single cell case - use a default height
                    const centerY = priceToCoordinate(currentPrice);
                    const defaultHeight = this._options.cellHeight || 12;
                    cellTop = centerY - defaultHeight / 2;
                    cellBottom = centerY + defaultHeight / 2;
                }
            } else if (i === fp.length - 1) {
                // Bottom cell: extend from midpoint with previous price level to current price
                const prevPrice = fp[i - 1].priceLevel;
                const midPrice = (prevPrice + currentPrice) / 2;
                cellTop = priceToCoordinate(midPrice);
                cellBottom = priceToCoordinate(currentPrice - (midPrice - currentPrice));
            } else {
                // Middle cells: extend from midpoint with previous to midpoint with next
                const prevPrice = fp[i - 1].priceLevel;
                const nextPrice = fp[i + 1].priceLevel;
                const topMidPrice = (prevPrice + currentPrice) / 2;
                const bottomMidPrice = (currentPrice + nextPrice) / 2;
                cellTop = priceToCoordinate(topMidPrice);
                cellBottom = priceToCoordinate(bottomMidPrice);
            }
            
            if ([cellTop, cellBottom].some(Number.isNaN)) continue;
            const buy = f.buyVolume || 0;
            const sell = f.sellVolume || 0;
            const total = buy + sell;
            const delta = buy - sell;
            const xPos = xPosStatic;
            const yPos = positionsBox(cellTop, cellBottom, v);
            let bg, alpha;
            if (!total) {
                bg = neutralColor;
                alpha = 0.02;
            } else if (this._options.showLowVolume && (buy < this._options.lowVolumeThreshold || sell < this._options.lowVolumeThreshold)) {
                // Use lowVolumeColor for cells where either buy or sell volume is less than lowVolumeThreshold
                bg = this._options.lowVolumeColor || '#FFB433';
                // Alpha based on the smaller of the two volumes - lower volume = more visible
                const minVolume = Math.min(buy, sell);
                // Reduce overall opacity: lower volume = higher alpha (more visible) but reduced overall
                alpha = Math.max(0.2, 0.6 - (minVolume / this._options.lowVolumeThreshold) * 0.3);
            } else {
                const ratio = (total - minVol) / volRange;
                const grad = delta >= 0 ? upGradient : downGradient;
                const idx = Math.min(grad.length - 1, Math.floor(ratio * grad.length));
                bg = grad[idx];
                alpha = Math.min(0.7, 0.25 + Math.min(1, Math.abs(delta / (total || 1))) * 0.45);
            }
            ctx.globalAlpha = alpha;
            ctx.fillStyle = bg;
            const radius = Math.min(2, Math.min(xPos.length, yPos.length) * 0.1);
            if (radius > 0.5 && (i === 0 || i === fp.length - 1)) {
                ctx.beginPath();
                if (i === 0) {
                    ctx.roundRect(xPos.position, yPos.position, xPos.length, yPos.length, [radius, radius, 0, 0]);
                } else {
                    ctx.roundRect(xPos.position, yPos.position, xPos.length, yPos.length, [0, 0, radius, radius]);
                }
                ctx.fill();
            } else {
                ctx.fillRect(xPos.position, yPos.position, xPos.length, yPos.length);
            }
            if (xPos.length >= 18 && yPos.length >= 8) {
                ctx.globalAlpha = 1;
                ctx.fillStyle = this._options.textColor || '#fff';
                ctx.fillText(
                    this._formatText(buy, sell, delta), 
                    xPos.position + xPos.length / 2, 
                    yPos.position + yPos.length / 2
                );
            }
            cellGeometry.set(f.priceLevel, {
                top: yPos.position,
                bottom: yPos.position + yPos.length,
                center: priceToCoordinate(f.priceLevel), // Use the exact price coordinate as center
                height: yPos.length
            });
        }
        
        // Draw COT High/Low markers if enabled
        if (this._options.showCOT !== false) {
            // Calculate COT for current bar directly
            this._drawCOTMarkers(ctx, d, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic, barIndex);
        }
        
        // Draw standard imbalance markers (percentage based) if enabled
        if (this._options.showImbalance !== false) {
            this._drawImbalanceIndicators(ctx, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic);
        }

        // Draw absolute-volume imbalance rectangles inside the cell when buy or sell volume
        // exceeds a user-configurable absolute threshold (imbalanceVolumeThreshold).
        // This is independent of the percent-based imbalance markers above.
        const absThreshold = typeof this._options.imbalanceVolumeThreshold === 'number' ? this._options.imbalanceVolumeThreshold : (this._options.imbalanceVolumeThreshold || 2000);
        const markerSize = typeof this._options.imbalanceMarkerSize === 'number' ? this._options.imbalanceMarkerSize : (this._options.imbalanceMarkerSize || 6);
        // reuse imbalance styling values so inner rectangles match imbalance markers
        const severeImbalanceThreshold = this._options.severeImbalanceThreshold || 500;
        const severeImbalanceColor = this._options.severeImbalanceColor || '#FFB433';
        const imbalanceMarkerAlpha = (typeof this._options.imbalanceMarkerAlpha === 'number') ? this._options.imbalanceMarkerAlpha : 1.0;
        if (absThreshold > 0) {
            // Render a thin vertical marker-rectangle (like the imbalance marker) inside the cell
            for (let i = 0; i < fp.length; ++i) {
                const f = fp[i];
                const geom = cellGeometry.get(f.priceLevel);
                if (!geom) continue;
                const buy = f.buyVolume || 0;
                const sell = f.sellVolume || 0;
                // If either side exceeds absolute threshold, draw
                if (buy >= absThreshold || sell >= absThreshold) {
                    const isBuyDominant = buy >= sell;
                    // thin vertical rectangle geometry
                    const rectH = Math.max(4, Math.min(Math.round(markerSize), Math.max(4, Math.floor(geom.height - 4))));
                    const rectW = Math.max(2, Math.round(markerSize / 3));
                    const rectY = Math.round((geom.center - rectH / 2) * v) / v;
                    const cellLeft = xPosStatic.position;
                    const cellWidth = xPosStatic.length;
                    let rectX;
                    // position rectangle just inside the left/right edge of the cell
                    // move the rectangle closer to the cell edge (smaller inset)
                    const inset = 1; // px inset from cell edge (smaller makes it closer)
                    if (isBuyDominant) {
                        rectX = Math.round((cellLeft + cellWidth - rectW - inset) * h) / h;
                    } else {
                        rectX = Math.round((cellLeft + inset) * h) / h;
                    }

                    // choose a single absolute-volume color (yellow) for both buy/sell rectangles
                    // configurable via this._options.imbalanceAbsoluteColor, fallback to yellow
                    const absoluteColor = this._options.imbalanceAbsoluteColor || '#fca311';
                    const fillStyle = absoluteColor;

                    // draw a small circle instead of a rectangle
                    ctx.globalAlpha = imbalanceMarkerAlpha;
                    ctx.fillStyle = fillStyle;
                    // compute circle center in device pixels then draw a circle with radius based on markerSize
                    const centerX = (rectX + rectW / 2) * h;
                    const centerY = (rectY + rectH / 2) * v;
                    // reduce circle size: smaller multiplier (0.25) for a compact dot
                    const circleR = Math.max(1, Math.round((markerSize * 0.18) * Math.max(h, v)));
                    try {
                        ctx.beginPath();
                        ctx.arc(centerX, centerY, circleR, 0, Math.PI * 2);
                        ctx.fill();
                    } catch (e) {
                        // fallback to a tiny filled rect if arc isn't available
                        ctx.fillRect((centerX - circleR) , (centerY - circleR), circleR * 2, circleR * 2);
                    }
                    ctx.globalAlpha = 1;
                }
            }
        }
        
        // Draw absorption markers if enabled
        if (this._options.showAbsorption !== false) {
            this._drawAbsorptionMarkers(ctx, d, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic, barIndex);
        }
        
        // Remove cumulative delta calculation and rendering
        if (this._options.showTable !== false && (d.volume || d.delta !== undefined)) {
            // Calculate lowY for table positioning - use the bottom of the lowest footprint cell
            const sortedFp = [...fp].sort((a, b) => a.priceLevel - b.priceLevel);
            const lowestPrice = sortedFp[0]?.priceLevel || d.low;
            
            // For dynamic sizing, extend below the lowest price level
            let tableLowY;
            if (sortedFp.length > 1) {
                const lowestPrice = sortedFp[0].priceLevel;
                const secondLowestPrice = sortedFp[1].priceLevel;
                const priceDiff = secondLowestPrice - lowestPrice;
                tableLowY = priceToCoordinate(lowestPrice - priceDiff / 2);
            } else {
                tableLowY = priceToCoordinate(lowestPrice) + (this._options.cellHeight || 12) / 2;
            }
            
            this._drawSummaryTable(ctx, d, startX, width, tableLowY, scope);
        }
    }

    _drawImbalanceIndicators(ctx, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const imbalanceThreshold = this._options.imbalanceThreshold || 300;
        // Severe imbalance threshold (percentage). If largerVolume >= severeImbalanceThreshold% * smallerVolume
        // we draw the imbalance marker in a more prominent color
        const severeImbalanceThreshold = this._options.severeImbalanceThreshold || 500;
        const severeImbalanceColor = this._options.severeImbalanceColor || '#FFB433';
        // Opacity for imbalance markers (0..1). Default increased to fully opaque for visibility.
        const imbalanceMarkerAlpha = (typeof this._options.imbalanceMarkerAlpha === 'number') ? this._options.imbalanceMarkerAlpha : 1.0;
    const markerSize = typeof this._options.imbalanceMarkerSize === 'number' ? this._options.imbalanceMarkerSize : (this._options.imbalanceMarkerSize || 6);
    const lineLength = markerSize;
        if (fp.length < 2) return;

        const sortedFp = [...fp].sort((a, b) => a.priceLevel - b.priceLevel);

        ctx.lineWidth = 1; // finer line for subtlety
        ctx.globalAlpha = imbalanceMarkerAlpha;

        // Precompute left/right x positions tight to footprint block
        const rightX = (xPosStatic.position + xPosStatic.length + 1);
        const leftX = (xPosStatic.position - 2);

        // Buy imbalance markers (right side)
        for (let i = 1; i < sortedFp.length; i++) {
            if (i - 1 === 0) continue; // skip very bottom baseline check per original logic
            const cur = sortedFp[i];
            const lower = sortedFp[i - 1];
            const curBuy = cur.buyVolume || 0;
            const lowerSell = lower.sellVolume || 0;
            if (this._detectImbalance(curBuy, lowerSell, imbalanceThreshold)) {
                const geom = cellGeometry.get(cur.priceLevel);
                if (geom) {
                    // Choose color based on severe imbalance
                    const isSevere = this._detectImbalance(curBuy, lowerSell, severeImbalanceThreshold);
                    ctx.strokeStyle = isSevere ? severeImbalanceColor : (this._options.imbalanceColor || '#8cc7a1');
                    ctx.beginPath();
                    ctx.moveTo(rightX * h, geom.center * v - (lineLength / 2) * v);
                    ctx.lineTo(rightX * h, geom.center * v + (lineLength / 2) * v);
                    ctx.stroke();
                }
            }
        }

        // Sell imbalance markers (left side)
        for (let i = 0; i < sortedFp.length - 1; i++) {
            const cur = sortedFp[i];
            const higher = sortedFp[i + 1];
            const curSell = cur.sellVolume || 0;
            const higherBuy = higher.buyVolume || 0;
            if (this._detectImbalance(curSell, higherBuy, imbalanceThreshold)) {
                const geom = cellGeometry.get(cur.priceLevel);
                if (geom) {
                    const isSevere = this._detectImbalance(curSell, higherBuy, severeImbalanceThreshold);
                    ctx.strokeStyle = isSevere ? severeImbalanceColor : (this._options.imbalanceColor || '#8cc7a1');
                    ctx.beginPath();
                    ctx.moveTo(leftX * h, geom.center * v - (lineLength / 2) * v);
                    ctx.lineTo(leftX * h, geom.center * v + (lineLength / 2) * v);
                    ctx.stroke();
                }
            }
        }
        ctx.globalAlpha = 1;
    }

    _detectImbalance(largerVolume, smallerVolume, thresholdPercent) {
        if (smallerVolume === 0) return largerVolume > 0;
        return largerVolume >= (thresholdPercent / 100) * smallerVolume;
    }

    /**
     * Detect and draw absorption markers
     * Absorption occurs when high volume appears at a price level but price doesn't move significantly
     */
    _drawAbsorptionMarkers(ctx, d, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic, barIndex) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        if (!Array.isArray(fp) || fp.length === 0) return;
        
        const absorptionVolumeThreshold = this._options.absorptionVolumeThreshold || 5000;
        const absorptionPriceThreshold = this._options.absorptionPriceThreshold || 0.02; // 2% of candle range
        const absorptionColor = this._options.absorptionColor || '#FF6B35'; 
        const buyAbsorptionColor = this._options.buyAbsorptionColor || '#00FF88'; 
        const sellAbsorptionColor = this._options.sellAbsorptionColor || '#FF4444';
        const markerSize = this._options.cotMarkerSize || 6; // Use same size as COT markers
        const markerAlpha = this._options.absorptionMarkerAlpha || 0.9;
        
        // Calculate price range for the candle
        const priceRange = Math.abs(d.high - d.low);
        const priceThreshold = priceRange * absorptionPriceThreshold;
        
        ctx.save();
        ctx.globalAlpha = markerAlpha;
        
        // Look for absorption in each footprint level
        for (const level of fp) {
            const totalVolume = (level.buyVolume || 0) + (level.sellVolume || 0);
            
            // Check if volume exceeds threshold
            if (totalVolume >= absorptionVolumeThreshold) {
                // Check if price level is within the absorption threshold from close
                const priceDistance = Math.abs(level.priceLevel - d.close);
                
                // Absorption detected if high volume occurred near the close price
                // This indicates the market absorbed the volume without significant price movement
                if (priceDistance <= priceThreshold) {
                    const geom = cellGeometry.get(level.priceLevel);
                    if (geom) {
                        // Determine absorption type based on dominant volume
                        const buyVolume = level.buyVolume || 0;
                        const sellVolume = level.sellVolume || 0;
                        const isBuyAbsorption = buyVolume > sellVolume;
                        const absorptionType = isBuyAbsorption ? 'buy' : 'sell';
                        const textColor = isBuyAbsorption ? buyAbsorptionColor : sellAbsorptionColor;
                        
                        // Position triangle on the right side of the cell
                        const triangleX = xPosStatic.position + xPosStatic.length + markerSize + 1;
                        const triangleY = geom.center;
                        
                        ctx.fillStyle = absorptionColor;
                        
                        // Draw right-facing triangle marker
                        ctx.beginPath();
                        const triangleWidth = markerSize * 0.6;
                        ctx.moveTo((triangleX - triangleWidth) * h, (triangleY - markerSize/2) * v);
                        ctx.lineTo((triangleX - triangleWidth) * h, (triangleY + markerSize/2) * v);
                        ctx.lineTo(triangleX * h, triangleY * v);
                        ctx.closePath();
                        ctx.fill();
                        
                        // Store absorption data for text positioning above COT
                        if (!this._absorptionData) this._absorptionData = [];
                        this._absorptionData.push({
                            volume: totalVolume,
                            type: absorptionType,
                            color: textColor,
                            x: xPosStatic.position + xPosStatic.length / 2,
                            y: triangleY
                        });
                    }
                }
            }
        }
        
        // Draw absorption volume text above COT High area
        if (this._absorptionData && this._absorptionData.length > 0) {
            // Find the highest price level for absorption text (above COT High)
            const sortedFp = [...fp].sort((a, b) => b.priceLevel - a.priceLevel);
            const highestFpLevel = sortedFp[0]?.priceLevel;
            
            if (highestFpLevel !== undefined) {
                const geom = cellGeometry.get(highestFpLevel);
                if (geom) {
                    const cotMarkerOffset = this._options.cotMarkerOffset || 16;
                    const cotLabelPadding = this._options.cotLabelPadding || 6;
                    const cotMarkerSize = this._options.cotMarkerSize || 6;
                    
                    // Position absorption text above COT High text
                    const absorptionTextY = geom.top - cotMarkerOffset - cotMarkerSize - cotLabelPadding - 15;
                    
                    ctx.font = this._font9;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'bottom';
                    
                    // Draw absorption text for each detected absorption
                    this._absorptionData.forEach((absorption, index) => {
                        ctx.fillStyle = absorption.color;
                        const textY = absorptionTextY - (index * 12);
                        ctx.fillText(
                            `A:${this._formatNum(absorption.volume)}`,
                            absorption.x * h,
                            textY * v
                        );
                    });
                }
            }
            
            // Clear absorption data for next frame
            this._absorptionData = [];
        }
        
        ctx.restore();
    }

    /**
     * Calculate COT High and Low values per candle
     * COT High: cumulative delta from high price level down to close price level
     * COT Low: cumulative delta from low price level up to close price level
     */
    _calculateCOT(d, fp, barIndex) {
        if (!fp || !fp.length || !d) return { cotHigh: 0, cotLow: 0, hasNewHigh: false, hasNewLow: false };
        
        const high = d.high;
        const low = d.low;
        const close = d.close;
        
        // Sort footprint by price level (highest to lowest)
        const sortedFp = [...fp].sort((a, b) => b.priceLevel - a.priceLevel);
        
        let cotHigh = 0;
        let cotLow = 0;
        
        // COT High: accumulate delta from high down to close
        for (const level of sortedFp) {
            if (level.priceLevel <= high && level.priceLevel >= close) {
                const delta = (level.buyVolume || 0) - (level.sellVolume || 0);
                cotHigh += delta;
            }
        }
        
        // COT Low: accumulate delta from low up to close
        for (const level of sortedFp) {
            if (level.priceLevel >= low && level.priceLevel <= close) {
                const delta = (level.buyVolume || 0) - (level.sellVolume || 0);
                cotLow += delta;
            }
        }
        
        return {
            cotHigh,
            cotLow,
            hasNewHigh: false, // Not relevant for per-candle calculation
            hasNewLow: false   // Not relevant for per-candle calculation
        };
    }

    /**
     * Draw COT High/Low markers above and below footprint cells
     */
    _drawCOTMarkers(ctx, d, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic, barIndex) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        const cotData = this._calculateCOT(d, fp, barIndex);
        const { cotHigh, cotLow } = cotData;
        
        const cotThreshold = this._options.cotThreshold || 10; // Lower threshold for per-candle COT
        const markerSize = this._options.cotMarkerSize || 8;
        const markerOffset = this._options.cotMarkerOffset || 12;
        
        ctx.save();
        ctx.globalAlpha = this._options.cotMarkerAlpha || 0.9;
        
        // Find the highest and lowest price levels in current footprint for marker positioning
        const sortedFp = [...fp].sort((a, b) => b.priceLevel - a.priceLevel);
        const highestFpLevel = sortedFp[0]?.priceLevel;
        const lowestFpLevel = sortedFp[sortedFp.length - 1]?.priceLevel;
        
        // Draw COT High marker (above the highest price level)
        if (highestFpLevel !== undefined) {
            const geom = cellGeometry.get(highestFpLevel);
            if (geom) {
                const markerY = geom.top - markerOffset;
                const markerX = xPosStatic.position + xPosStatic.length / 2;
                
                // Choose color based on COT value
                if (cotHigh === 0) {
                    ctx.fillStyle = this._options.cotZeroColor || '#FFB433';
                } else {
                    ctx.fillStyle = cotHigh > 0 ? 
                        (this._options.cotHighColor || '#00C853') : 
                        (this._options.cotLowColor || '#FF1744');
                }
                
                // Draw triangle for COT High; orientation depends on sign
                const mxH = markerX * h;
                const myH = markerY * v;
                const halfW = (markerSize / 2) * h;
                const vertH = markerSize * v;
                ctx.beginPath();
                if (cotHigh >= 0) {
                    // Upward pointing triangle (apex at myH)
                    ctx.moveTo(mxH, myH);
                    ctx.lineTo(mxH - halfW, myH + vertH);
                    ctx.lineTo(mxH + halfW, myH + vertH);
                } else {
                    // Downward pointing triangle located at same markerY (base at myH, apex below)
                    ctx.moveTo(mxH, myH + vertH);
                    ctx.lineTo(mxH - halfW, myH);
                    ctx.lineTo(mxH + halfW, myH);
                }
                ctx.closePath();
                ctx.fill();
                // Draw COT value text (CH label always above the marker)
                ctx.globalAlpha = this._options.cotMarkerAlpha || 0.9;
                ctx.fillStyle = this._options.textColor || '#fff';
                ctx.font = this._font9;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'bottom';
                const cotText = `CH ${cotHigh >= 0 ? '+' : ''}${this._formatDelta(cotHigh)}`;
                const labelPad = this._options.cotLabelPadding || 6;
                ctx.fillText(cotText, mxH, myH - (2 * v + labelPad));
            }
        }
        
        // Draw COT Low marker (below the lowest price level)
        if (lowestFpLevel !== undefined) {
            const geom = cellGeometry.get(lowestFpLevel);
            if (geom) {
                const markerY = geom.bottom + markerOffset;
                const markerX = xPosStatic.position + xPosStatic.length / 2;
                
                // Choose color based on COT value
                if (cotLow === 0) {
                    ctx.fillStyle = this._options.cotZeroColor || '#FFB433';
                } else {
                    ctx.fillStyle = cotLow > 0 ? 
                        (this._options.cotHighColor || '#00C853') : 
                        (this._options.cotLowColor || '#FF1744');
                }
                
                // Draw triangle for COT Low; orientation depends on sign
                const mxL = markerX * h;
                const myL = markerY * v;
                const halfWL = (markerSize / 2) * h;
                const vertL = markerSize * v;
                ctx.beginPath();
                if (cotLow >= 0) {
                    // Upward pointing triangle: apex above markerY
                    ctx.moveTo(mxL, myL - vertL);
                    ctx.lineTo(mxL - halfWL, myL);
                    ctx.lineTo(mxL + halfWL, myL);
                } else {
                    // Downward pointing triangle: apex below markerY
                    ctx.moveTo(mxL, myL + vertL);
                    ctx.lineTo(mxL - halfWL, myL);
                    ctx.lineTo(mxL + halfWL, myL);
                }
                ctx.closePath();
                ctx.fill();
                // Draw COT value text (CL label always below the triangle)
                ctx.globalAlpha = this._options.cotMarkerAlpha || 0.9;
                ctx.fillStyle = this._options.textColor || '#fff';
                ctx.font = this._font9;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'top';
                const cotText = `CL ${cotLow >= 0 ? '+' : ''}${this._formatDelta(cotLow)}`;
                const labelPad = this._options.cotLabelPadding || 6;
                const labelY = cotLow >= 0 ? (myL + 2 * v + labelPad) : (myL + vertL + 2 * v + labelPad);
                ctx.fillText(cotText, mxL, labelY);
            }
        }
        
        ctx.restore();
    }

    /**
     * Helper method to find the current bar index in the visible range
     */
    _findBarIndexInRange(visibleIndex) {
        if (!this._data?.visibleRange) return -1;
        return this._data.visibleRange.from + visibleIndex;
    }

    _drawSummaryTable(ctx, d, startX, width, lowY, scope) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
    const showCumul = this._options?.showCumulative !== false;
    // if cumulative display enabled, add an extra row
    const tableHeight = showCumul ? 48 : 28;
        // Push table down to make room for COT markers (add extra offset)
        const cotOffset = this._options.showCOT !== false ? 
            ((this._options.cotMarkerOffset || 16) + (this._options.cotMarkerSize || 6) + (this._options.cotLabelPadding || 6) + 8) : 0;
        const tableY = lowY + 10 + cotOffset;
        ctx.globalAlpha = 0.9;
        ctx.fillStyle = this._options.tableBackgroundColor || '#1E222D';
        const xPos = positionsLine(startX + width / 2, h, width);
        const yPos = positionsBox(tableY, tableY + tableHeight, v);
        const radius = 3;
        ctx.beginPath();
        ctx.roundRect(xPos.position, yPos.position, xPos.length, yPos.length, radius);
        ctx.fill();
        ctx.globalAlpha = 1;
        ctx.strokeStyle = this._options.tableBorderColor || '#2A2E39';
        ctx.lineWidth = 1;
        ctx.stroke();
        ctx.font = this._font9;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';        
        const tableRows = showCumul ? 3 : 2;
        const centerX = xPos.position + xPos.length / 2;
        const lineHeight = (yPos.length - 8) / tableRows;
        const startY = yPos.position + 4 + lineHeight / 2;
        ctx.fillStyle = this._options.textColor || '#fff';
        ctx.fillText(`Vol: ${this._formatNum(d.volume || 0)}`, centerX, startY);
        const delta = d.delta || 0;
        ctx.fillStyle = delta >= 0 ? this._options.upColor : this._options.downColor;        
        ctx.fillText(`Δ ${this._formatDelta(Math.abs(delta))}`, centerX, startY + lineHeight);
        // draw cumulative delta under the delta 
        if (showCumul) {
            const cdVal = (d.cum_delta !== undefined) ? d.cum_delta : (d.cumDelta !== undefined ? d.cumDelta : 0);            
            if (typeof cdVal === 'number') {
                if (cdVal > 0) ctx.fillStyle = this._options.upColor;
                else if (cdVal < 0) ctx.fillStyle = this._options.downColor;
                else ctx.fillStyle = this._options.tableTextColor || this._options.textColor || '#fff';
            } else {
                ctx.fillStyle = this._options.tableTextColor || this._options.textColor || '#fff';
            }
            ctx.fillText(`ΣΔ ${this._formatDelta(Math.abs(cdVal))}`, centerX, startY + lineHeight * 2);
        }
        ctx.globalAlpha = 1;
    }

    _formatNum(n) {
        // original/default formatting: integers as-is, thousands/millions with 1 decimal
        if (typeof n !== 'number') n = Number(n) || 0;
        if (n < 1000) return n.toString();
        if (n >= 1e6) return (n / 1e6).toFixed(1) + 'M';
        return (n / 1e3).toFixed(1) + 'K';
    }
    
    _formatDelta(n) {
    // Delta-specific formatter: show up to two decimals, but strip trailing zeros
    // e.g., 5.00 -> "5", 5.20 -> "5.2", 5.25 -> "5.25"
    if (typeof n !== 'number') n = Number(n) || 0;
    const trim = (s) => String(s).replace(/\.?0+$/, '');
    if (n < 1000) return trim(n.toFixed(2));
    if (n >= 1e6) return trim((n / 1e6).toFixed(2)) + 'M';
    return trim((n / 1e3).toFixed(2)) + 'K';
    }

    _formatText(buy, sell, delta) {
        switch (this._options.volumeDisplayMode) {
            case 'split': return `${this._formatNum(sell)}x${this._formatNum(buy)}`;
            case 'delta': return `${this._formatNum(Math.abs(delta))}`;
            case 'total': return this._formatNum(buy + sell);
            default: return `${this._formatNum(sell)}x${this._formatNum(buy)}`;
        }
    }
}

/**
 * Optimized footprint series with proper OHLC support for MagnetOHLC
 * Fixed to create separate renderer instances per chart to prevent blinking in multi-chart layouts
 */
export const FootprintSeries = {
    // Create a new instance for each chart instead of sharing
    create() {
        return Object.create(this, {
            _renderer: { value: null, writable: true },
            _options: { value: null, writable: true },
            _defaultOptions: { value: null, writable: true }
        });
    },
    
    renderer() {
        if (!this._renderer) {
            this._renderer = new FootprintRenderer();
        }
        return this._renderer;
    },
    
    update(data, options) {
        if (!this._renderer) {
            this._renderer = new FootprintRenderer();
        }
        this._options = this._options ? Object.assign(this._options, options) : Object.assign({}, this.defaultOptions(), options);
        this._renderer.update(data, this._options);
    },
    
    /**
     * Price value builder for MagnetOHLC - returns [open, high, low, close]
     * This is critical for proper crosshair magnetization to OHLC values
     */
    priceValueBuilder(row) {
        if (!isValidOHLCData(row)) {
            return [0, 0, 0, 0];
        }
        return [row.open, row.high, row.low, row.close];
    },
    
    isWhitespace(row) {
        return !isValidOHLCData(row);
    },
    
    defaultOptions() {
        if (!this._defaultOptions) {
            this._defaultOptions = {
                upColor: '#089981', 
                downColor: '#F23645', 
                borderUpColor: '#089981', 
                borderDownColor: '#F23645',
                wickUpColor: '#089981', 
                wickDownColor: '#F23645', 
                borderVisible: true, 
                wickVisible: true,
                neutralColor: '#B2B5BE', 
                cellBorderColor: '#333', 
                textColor: '#fff',
                lowVolumeColor: '#FFB433',
                lowVolumeThreshold: 99,
                showLowVolume: true,
                volumeDisplayMode: 'split', 
                visible: true, 
                showTable: true, 
                tableBackgroundColor: '#1E222D', 
                tableBorderColor: '#2A2E39',
                cellHeight: 12, // Height of each footprint cell in pixels
                showImbalance: true,
                imbalanceThreshold: 300,
                imbalanceColor: '#BAD7E9',
                // Opacity for imbalance markers (0 = fully transparent, 1 = fully opaque)
                imbalanceMarkerAlpha: 1.0,
                // Threshold (percentage) for very large/severe imbalance to show a distinct color (e.g. 500%)
                severeImbalanceThreshold: 500,
                // Color used when imbalance meets or exceeds severeImbalanceThreshold
                severeImbalanceColor: '#FFB433',
                // COT (Commitment of Traders) High/Low options
                showCOT: true,
                cotMarkerSize: 6, // Size of COT marker triangles (reduced)
                cotMarkerOffset: 16, // Distance from footprint cells (increased for spacing)
                cotLabelPadding: 6, // Extra padding between marker and its label / table
                cotMarkerAlpha: 0.9, // Opacity of COT markers
                cotHighColor: '#00C853', // Color for positive COT values
                cotLowColor: '#FF1744', // Color for negative COT values
                cotZeroColor: '#FFB433', // Color for zero COT values
                // Absorption Detection options
                showAbsorption: true,
                absorptionVolumeThreshold: 5000, // Minimum volume required to detect absorption
                absorptionPriceThreshold: 0.02, // Maximum price movement % to consider absorption
                absorptionColor: '#FF6B35', // Color for absorption markers
                buyAbsorptionColor: '#00FF88', // Color for buy absorption text
                sellAbsorptionColor: '#FF4444', // Color for sell absorption text
                absorptionMarkerAlpha: 0.9 // Opacity of absorption markers

            };
        }
        return this._defaultOptions;
    },
    
    applyOptions(options) {
        this._options = Object.assign({}, this._options || this.defaultOptions(), options);
        return this;
    },
    
    destroy() {
        if (this._renderer?._textCache) {
            this._renderer._textCache.clear();
        }
        this._renderer = null;
        this._options = null;
        this._defaultOptions = null;
    }
};
