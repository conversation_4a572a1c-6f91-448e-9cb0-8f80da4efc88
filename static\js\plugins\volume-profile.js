// --- Volume Profile Plugin for TradeLab ---
// Calculates and displays volume profile from footprint data in real-time
// Shows horizontal bars in the center of each trading day

import { positionsBox, positionsLine } from './chart-utils.js';

/**
 * Pixel-Perfect Rendering Utilities for Volume Profile
 * Based on TradingView Lightweight Charts best practices
 */

/**
 * Interface for bitmap position and length
 */
class BitmapPositionLength {
    constructor(position, length) {
        this.position = position;
        this.length = length;
    }
}

/**
 * Calculate center offset for centering shapes
 * @param {number} lineBitmapWidth - Width in bitmap coordinates
 * @returns {number} Center offset
 */
function centreOffset(lineBitmapWidth) {
    return Math.floor(lineBitmapWidth * 0.5);
}

/**
 * Calculate pixel-perfect position for centered shapes
 * @param {number} positionMedia - Position in media coordinates
 * @param {number} pixelRatio - Horizontal or vertical pixel ratio
 * @param {number} desiredWidthMedia - Desired width in media coordinates
 * @param {boolean} widthIsBitmap - Whether width is already in bitmap coordinates
 * @returns {BitmapPositionLength} Position and length in bitmap coordinates
 */
function positionsLinePerfect(positionMedia, pixelRatio, desiredWidthMedia = 1, widthIsBitmap = false) {
    const scaledPosition = Math.round(pixelRatio * positionMedia);
    const lineBitmapWidth = widthIsBitmap
        ? desiredWidthMedia
        : Math.round(desiredWidthMedia * pixelRatio);
    const offset = centreOffset(lineBitmapWidth);
    const position = scaledPosition - offset;
    return new BitmapPositionLength(position, lineBitmapWidth);
}

/**
 * Calculate pixel-perfect position for shapes between two points
 * @param {number} position1Media - First position in media coordinates
 * @param {number} position2Media - Second position in media coordinates
 * @param {number} pixelRatio - Pixel ratio for the axis
 * @returns {BitmapPositionLength} Position and length in bitmap coordinates
 */
function positionsBoxPerfect(position1Media, position2Media, pixelRatio) {
    const scaledPosition1 = Math.round(pixelRatio * position1Media);
    const scaledPosition2 = Math.round(pixelRatio * position2Media);
    return new BitmapPositionLength(
        Math.min(scaledPosition1, scaledPosition2),
        Math.abs(scaledPosition2 - scaledPosition1) + 1
    );
}

/**
 * Volume Profile Data Point interface
 */
class VolumeProfileDataPoint {
    constructor(price, buyVolume = 0, sellVolume = 0, trades = 0) {
        this.price = price;
        this.buyVolume = buyVolume;
        this.sellVolume = sellVolume;
        this.totalVolume = buyVolume + sellVolume;
        this.delta = buyVolume - sellVolume;
        this.trades = trades;
    }

    addVolume(buyVol, sellVol, tradeCount = 1) {
        this.buyVolume += buyVol;
        this.sellVolume += sellVol;
        this.totalVolume += (buyVol + sellVol);
        this.delta = this.buyVolume - this.sellVolume;
        this.trades += tradeCount;
    }
}

/**
 * Daily Volume Profile Data
 */
class DailyVolumeProfile {
    constructor(dayKey, startTime, endTime, numberOfRows = 24, valueAreaPercentage = 70) {
        this.dayKey = dayKey;
        this.startTime = startTime;
        this.endTime = endTime;
        this.centerTime = startTime + (endTime - startTime) / 2;
        this.candles = [];
        this.volumeData = null;
        this.priceRange = null;
        this.numberOfRows = Math.max(5, Math.min(100, numberOfRows)); // Clamp between 5-100
        this.valueAreaPercentage = Math.max(50, Math.min(95, valueAreaPercentage)); // Clamp between 50-95%
        this.calculated = false;
        this._lastCandleCount = 0;
        this._calculationThrottled = false;
        this._volumeCache = new Map(); // Cache for volume calculations
        // Bump this on any intra-day update so views can detect changes without candle count growth
        this._updateVersion = 0;
        // Incremental price tracking for performance
        this._minPrice = null;
        this._maxPrice = null;
        this._priceRangeValid = false;
        this._lastFingerprint = null;
    }

    addFootprintData(candle) {
        if (!candle?.footprint || !Array.isArray(candle.footprint)) return;
        if (typeof candle.high !== 'number' || typeof candle.low !== 'number') return;
        
        // Check if this is a new candle or update to existing
        const existingIndex = this.candles.findIndex(c => c.time === candle.time);
        
        if (existingIndex >= 0) {
            // Update existing candle
            this.candles[existingIndex] = candle;
            // Always invalidate profile on updates so live changes reflect immediately
            this.calculated = false;
            this._updateVersion++;
        } else {
            // Add new candle
            this.candles.push(candle);
            // Invalidate on additions as well
            this.calculated = false;
            this._updateVersion++;
        }
        
        // Incrementally update price range for performance
        if (this._minPrice === null || candle.low < this._minPrice) {
            this._minPrice = candle.low;
            this._priceRangeValid = false;
        }
        if (this._maxPrice === null || candle.high > this._maxPrice) {
            this._maxPrice = candle.high;
            this._priceRangeValid = false;
        }
    }

    calculateVolumeProfile() {
        // Early returns for performance
        if (this.candles.length === 0) return;
        
        // Smart caching: Create data fingerprint to detect if recalculation is needed
        const currentFingerprint = this._calculateDataFingerprint();
        if (this.calculated && this._lastFingerprint === currentFingerprint) {
            return; // No changes detected, use cached result
        }
        
        // Throttle expensive recalculations
        if (this._calculationThrottled) return;
        this._calculationThrottled = true;
        setTimeout(() => { this._calculationThrottled = false; }, 100); // Throttle to 10fps for better performance

        // Calculate price range efficiently using incremental tracking
        let pHST, pLST;
        if (this._priceRangeValid && this._minPrice !== null && this._maxPrice !== null) {
            pHST = this._maxPrice;
            pLST = this._minPrice;
        } else {
            // Fallback to full scan only when necessary (e.g., first calculation)
            if (this._minPrice === null || this._maxPrice === null) {
                let minPrice = Infinity;
                let maxPrice = -Infinity;
                this.candles.forEach(candle => {
                    if (candle.high > maxPrice) maxPrice = candle.high;
                    if (candle.low < minPrice) minPrice = candle.low;
                });
                this._minPrice = minPrice;
                this._maxPrice = maxPrice;
            }
            pHST = this._maxPrice;
            pLST = this._minPrice;
            this._priceRangeValid = true;
        }
        const pSTP = (pHST - pLST) / this.numberOfRows; // Price step (like Pine Script)
        
        if (pSTP <= 0) return;
        
        this.priceRange = { high: pHST, low: pLST, step: pSTP };
        
        // Initialize arrays like Pine Script
        const rpVST = new Array(this.numberOfRows + 1).fill(0); // Total volume
        const rpVSB = new Array(this.numberOfRows + 1).fill(0); // Buy volume
        
        // Process each candle like Pine Script loop
        this.candles.forEach(candle => {
            const barHigh = candle.high;
            const barLow = candle.low;
            
            // Cache volume calculation per candle to avoid repeated footprint processing
            let barVolume;
            if (typeof candle.volume === 'number' && candle.volume > 0) {
                barVolume = candle.volume;
            } else {
                // Use cached volume if available, otherwise calculate and cache
                if (!candle._cachedVolume) {
                    candle._cachedVolume = this._estimateVolumeFromFootprint(candle.footprint);
                }
                barVolume = candle._cachedVolume;
            }
            
            const isBullish = candle.close > candle.open;
            
            // Pre-calculate volume weight to avoid division in inner loop when possible
            const candleRange = barHigh - barLow;
            const hasRange = candleRange > 0;
            
            // Loop through price levels like Pine Script: for pLL = pLST to pHST by pSTP
            for (let l = 0; l < this.numberOfRows; l++) {
                const pLL = pLST + l * pSTP; // Price level low (like Pine Script)
                const pLH = pLL + pSTP;      // Price level high
                
                // Check if candle overlaps this price level (like Pine Script condition)
                if (barHigh >= pLL && barLow < pLH) {
                    // Volume weighting formula from Pine Script
                    const volumeWeight = hasRange ? pSTP / candleRange : 1;
                    const allocatedVolume = barVolume * volumeWeight;
                    
                    rpVST[l] += allocatedVolume; // Add to total volume
                    
                    if (isBullish) {
                        rpVSB[l] += allocatedVolume; // Add to buy volume
                    }
                }
            }
        });
        
        // Convert to volume data format for rendering
        this.volumeData = [];
        const maxVolume = Math.max(...rpVST);
        
        // Find per-day maximum absolute delta so delta bars can utilize full allocated width
        let maxAbsDelta = 0;
        for (let l = 0; l < this.numberOfRows; l++) {
            if (rpVST[l] > 0) {
                const bbp = 2 * rpVSB[l] - rpVST[l];
                const absBbp = Math.abs(bbp);
                if (absBbp > maxAbsDelta) maxAbsDelta = absBbp;
            }
        }

        for (let l = 0; l < this.numberOfRows; l++) {
            if (rpVST[l] > 0) {
                const bbp = 2 * rpVSB[l] - rpVST[l]; // Delta calculation from Pine Script
                const priceLevel = pLST + (l + 0.5) * pSTP; // Center of price level
                
                this.volumeData.push({
                    price: priceLevel,
                    totalVolume: rpVST[l],
                    buyVolume: rpVSB[l],
                    sellVolume: rpVST[l] - rpVSB[l],
                    delta: bbp,
                    isDeltaPositive: bbp > 0,
                    volumePercent: maxVolume > 0 ? rpVST[l] / maxVolume : 0,
                    // Normalize delta to the max absolute delta of the day (wider and relative to delta distribution)
                    deltaPercent: maxAbsDelta > 0 ? Math.abs(bbp) / maxAbsDelta : 0,
                    priceLevelLow: pLST + l * pSTP,      // Exact bounds like Pine Script
                    priceLevelHigh: pLST + (l + 1) * pSTP // Exact bounds like Pine Script
                });
            }
        }
        
        this.calculated = true;
        this._lastCandleCount = this.candles.length;
        this._lastFingerprint = currentFingerprint; // Store fingerprint for next comparison
        
        // Cache the calculation for future reference with size limit
        const cacheKey = `${this.dayKey}_${this.candles.length}`;
        this._volumeCache.set(cacheKey, this.volumeData);
        
        // Limit cache size to prevent memory leaks
        if (this._volumeCache.size > 100) {
            // Remove oldest entries (FIFO)
            const oldestKey = this._volumeCache.keys().next().value;
            this._volumeCache.delete(oldestKey);
        }
    }

    // Calculate a fast fingerprint of current data to detect changes
    _calculateDataFingerprint() {
        if (this.candles.length === 0) return 0;
        
        // Create a lightweight hash from key data points
        let hash = this.candles.length * 31;
        
        // Sample first, last, and middle candles for fingerprint
        const first = this.candles[0];
        const last = this.candles[this.candles.length - 1];
        const middle = this.candles[Math.floor(this.candles.length / 2)];
        
        // Include key price and volume data
        hash = hash * 31 + (first.high * 1000 | 0);
        hash = hash * 31 + (first.low * 1000 | 0);
        hash = hash * 31 + (first.volume | 0);
        hash = hash * 31 + (last.high * 1000 | 0);
        hash = hash * 31 + (last.low * 1000 | 0);
        hash = hash * 31 + (last.volume | 0);
        hash = hash * 31 + (middle.high * 1000 | 0);
        hash = hash * 31 + (middle.low * 1000 | 0);
        hash = hash * 31 + (middle.volume | 0);
        
        // Include price range
        hash = hash * 31 + (this._minPrice * 1000 | 0);
        hash = hash * 31 + (this._maxPrice * 1000 | 0);
        
        return hash;
    }

    // Optimized fallback to estimate a candle's total volume from its footprint levels
    _estimateVolumeFromFootprint(footprint) {
        if (!Array.isArray(footprint) || footprint.length === 0) return 0;
        
        let vol = 0, buy = 0, sell = 0;
        
        // Optimized loop with early exit patterns
        for (let i = 0; i < footprint.length; i++) {
            const lvl = footprint[i];
            if (lvl == null) continue;
            
            // Fast path for simple number format
            if (typeof lvl === 'number') {
                vol += lvl;
                continue;
            }
            
            // Batch property checks for better performance
            const hasVolume = typeof lvl.volume === 'number';
            const hasV = typeof lvl.v === 'number';
            const hasBuy = typeof lvl.buy === 'number';
            const hasB = typeof lvl.b === 'number';
            const hasSell = typeof lvl.sell === 'number';
            const hasS = typeof lvl.s === 'number';
            
            if (hasVolume) vol += lvl.volume;
            if (hasV) vol += lvl.v;
            if (hasBuy) buy += lvl.buy;
            if (hasB) buy += lvl.b;
            if (hasSell) sell += lvl.sell;
            if (hasS) sell += lvl.s;
        }
        
        // Return total volume or sum of buy/sell if total not available
        return vol > 0 ? vol : buy + sell;
    }

    getProfile() {
        if (!this.calculated) {
            this.calculateVolumeProfile();
        }
        return this.volumeData || [];
    }

    /**
     * Calculate POC (Point of Control) - price level with highest volume
     * @returns {Object|null} POC data with price and volume
     */
    getPOC() {
        const profile = this.getProfile();
        if (!profile || profile.length === 0) return null;
        
        let pocLevel = profile[0];
        for (const level of profile) {
            if (level.totalVolume > pocLevel.totalVolume) {
                pocLevel = level;
            }
        }
        return {
            price: pocLevel.price,
            volume: pocLevel.totalVolume,
            buyVolume: pocLevel.buyVolume,
            sellVolume: pocLevel.sellVolume
        };
    }

    /**
     * Calculate Value Area High and Low (configurable % of total volume)
     * @returns {Object|null} Value area with high, low, and POC
     */
    getValueArea() {
        const profile = this.getProfile();
        if (!profile || profile.length === 0) return null;
        
        const poc = this.getPOC();
        if (!poc) return null;
        
        // Note: Profile array is already sorted by price (ascending) from calculateVolumeProfile()
        // No need to sort again as it's built from low to high price levels
        
        // Calculate total volume
        const totalVolume = profile.reduce((sum, level) => sum + level.totalVolume, 0);
        const valueAreaVolume = totalVolume * (this.valueAreaPercentage / 100); // Use configurable percentage
        
        // Find POC index in profile (already sorted by price)
        let pocIndex = -1;
        for (let i = 0; i < profile.length; i++) {
            if (Math.abs(profile[i].price - poc.price) < 0.01) {
                pocIndex = i;
                break;
            }
        }
        
        if (pocIndex === -1) return null;
        
        // Start from POC and expand both directions to capture configured % of volume
        let accumulatedVolume = profile[pocIndex].totalVolume;
        let upperIndex = pocIndex;  // Will expand upward (higher prices)
        let lowerIndex = pocIndex;  // Will expand downward (lower prices)
        
        // Expand alternately in both directions, preferring the side with more volume
        while (accumulatedVolume < valueAreaVolume && (upperIndex < profile.length - 1 || lowerIndex > 0)) {
            const canExpandUp = upperIndex < profile.length - 1;
            const canExpandDown = lowerIndex > 0;
            
            if (!canExpandUp && !canExpandDown) break;
            
            const upperVolume = canExpandUp ? profile[upperIndex + 1].totalVolume : 0;
            const lowerVolume = canExpandDown ? profile[lowerIndex - 1].totalVolume : 0;
            
            // Expand to the side with more volume, or up if equal
            if (canExpandUp && (!canExpandDown || upperVolume >= lowerVolume)) {
                upperIndex++;
                accumulatedVolume += profile[upperIndex].totalVolume;
            } else if (canExpandDown) {
                lowerIndex--;
                accumulatedVolume += profile[lowerIndex].totalVolume;
            }
        }
        
        // Ensure VAH >= POC >= VAL
        const vahPrice = profile[upperIndex].price;
        const valPrice = profile[lowerIndex].price;
        const pocPrice = poc.price;
        
        // Validation: VAH should be >= POC >= VAL
        if (vahPrice < pocPrice || pocPrice < valPrice) {
            // Invalid Value Area calculation detected (VAH >= POC >= VAL should hold)
            // Silenced previous console warning per request to remove console statements.
        }
        
        return {
            high: Math.max(vahPrice, pocPrice),  // Ensure VAH >= POC
            low: Math.min(valPrice, pocPrice),   // Ensure VAL <= POC
            poc: pocPrice,
            volumePercentage: (accumulatedVolume / totalVolume) * 100
        };
    }
}

/**
 * Volume Profile Data Manager - Modified for Daily Center Display
 */
class VolumeProfileDataManager {
    constructor() {
        this.dailyProfiles = new Map(); // dayKey -> DailyVolumeProfile
        this.tickSize = 0.05;
        this.numberOfRows = 24; // Default number of volume profile bars
        this.valueAreaPercentage = 70; // Default value area percentage
        this.allCandles = []; // Store all candles to determine day boundaries
        this._rebuildThrottled = false;
        this._lastRebuildTime = 0;
        this._profileCache = new Map(); // Cache for expensive calculations
    }

    setTickSize(tickSize) {
        this.tickSize = tickSize || 0.05;
    }

    setNumberOfRows(numberOfRows) {
        this.numberOfRows = Math.max(5, Math.min(100, numberOfRows || 24));
        // Clear cache and force rebuild when number of rows changes
        this._profileCache.clear();
        this.rebuildDailyProfiles();
    }

    setValueAreaPercentage(percentage) {
        this.valueAreaPercentage = Math.max(50, Math.min(95, percentage || 70));
        // Clear cache and force rebuild when value area percentage changes
        this._profileCache.clear();
        this.rebuildDailyProfiles();
    }

    getDayKey(timestamp) {
        const date = new Date(timestamp * 1000);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }

    addFootprintData(candle) {
        if (!candle?.footprint) return;

        // Check if this is a new candle or update to existing
        const existingIndex = this.allCandles.findIndex(c => c.time === candle.time);
        
        if (existingIndex >= 0) {
            // Update existing candle
            this.allCandles[existingIndex] = {
                time: candle.time,
                high: candle.high,
                low: candle.low,
                open: candle.open,
                close: candle.close,
                volume: candle.volume || 0,
                dayKey: this.getDayKey(candle.time),
                footprint: candle.footprint
            };
        } else {
            // Add new candle
            this.allCandles.push({
                time: candle.time,
                high: candle.high,
                low: candle.low,
                open: candle.open,
                close: candle.close,
                volume: candle.volume || 0,
                dayKey: this.getDayKey(candle.time),
                footprint: candle.footprint
            });
        }

        // Memory optimization: Limit historical data to prevent memory leaks
        const MAX_CANDLES = 10000; // Keep reasonable limit
        if (this.allCandles.length > MAX_CANDLES) {
            // Remove oldest 10% of candles
            const removeCount = Math.floor(MAX_CANDLES * 0.1);
            this.allCandles.splice(0, removeCount);
        }

        // Smart throttling based on data volume
        const now = Date.now();
        const timeSinceLastRebuild = now - this._lastRebuildTime;
        const shouldRebuild = !this._rebuildThrottled && (
            timeSinceLastRebuild > 200 || // Always rebuild after 200ms
            (this.allCandles.length % 100 === 0 && timeSinceLastRebuild > 100) // Less frequent updates for better performance
        );
        
        if (shouldRebuild) {
            this._rebuildThrottled = true;
            this._lastRebuildTime = now;
            
            // Use requestIdleCallback for better performance
            const rebuildFunction = () => {
                this.rebuildDailyProfiles();
                this._rebuildThrottled = false;
            };
            
            if (typeof requestIdleCallback !== 'undefined') {
                requestIdleCallback(rebuildFunction, { timeout: 200 });
            } else {
                requestAnimationFrame(rebuildFunction);
            }
        }
    }

    rebuildDailyProfiles() {
        // Sort candles by time once
        this.allCandles.sort((a, b) => a.time - b.time);
        
        // Group candles by day efficiently
        const dayGroups = new Map();
        for (const candle of this.allCandles) {
            if (!dayGroups.has(candle.dayKey)) {
                dayGroups.set(candle.dayKey, []);
            }
            dayGroups.get(candle.dayKey).push(candle);
        }

        // Only update profiles that have changed
        const existingKeys = new Set(this.dailyProfiles.keys());
        const newKeys = new Set(dayGroups.keys());
        
        // Remove outdated profiles
        for (const key of existingKeys) {
            if (!newKeys.has(key)) {
                this.dailyProfiles.delete(key);
            }
        }

        // Create or update daily profiles
        dayGroups.forEach((candles, dayKey) => {
            if (candles.length === 0) return;

            const startTime = Math.min(...candles.map(c => c.time));
            const endTime = Math.max(...candles.map(c => c.time));
            
            let dailyProfile = this.dailyProfiles.get(dayKey);
            
            if (!dailyProfile) {
                dailyProfile = new DailyVolumeProfile(dayKey, startTime, endTime, this.numberOfRows, this.valueAreaPercentage);
                this.dailyProfiles.set(dayKey, dailyProfile);
            } else {
                // Update time boundaries if needed
                dailyProfile.startTime = startTime;
                dailyProfile.endTime = endTime;
                dailyProfile.centerTime = startTime + (endTime - startTime) / 2;
                // Update numberOfRows if it has changed
                if (dailyProfile.numberOfRows !== this.numberOfRows) {
                    dailyProfile.numberOfRows = this.numberOfRows;
                    dailyProfile.calculated = false; // Force recalculation
                }
                // Update valueAreaPercentage if it has changed
                if (dailyProfile.valueAreaPercentage !== this.valueAreaPercentage) {
                    dailyProfile.valueAreaPercentage = this.valueAreaPercentage;
                    dailyProfile.calculated = false; // Force recalculation
                }
            }
            
            // Add new candles first (fast path)
            const currentCandleCount = dailyProfile.candles.length;
            if (candles.length > currentCandleCount) {
                const newCandles = candles.slice(currentCandleCount);
                newCandles.forEach(candle => {
                    dailyProfile.addFootprintData(candle);
                });
            }

            // Always forward the most recent candle to capture live intra-bar updates
            const last = candles[candles.length - 1];
            if (last) {
                dailyProfile.addFootprintData(last);
            }
        });
    }

    getAllDailyProfiles() {
        return Array.from(this.dailyProfiles.values());
    }

    clear() {
        this.dailyProfiles.clear();
        this.allCandles = [];
    }
}

/**
 * Side Volume Profile Renderer - Right Side Display
 */
class SideVolumeProfileRenderer {
    constructor(data) {
        this._data = data;
        this._volumeFont = '8px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
        this._priceFont = '8px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
    }

    draw(target, priceConverter) {
        if (!this._data || this._data._disabled) return;
        if (!this._data?.showSideProfile) return;
        if (!this._data?.currentDayProfile) return;
        
        target.useBitmapCoordinateSpace(scope => {
            if (this._data && !this._data._disabled && this._data.showSideProfile && this._data.currentDayProfile) {
                this._drawSideProfileImpl(scope, priceConverter);
            }
        });
    }

    _drawSideProfileImpl(scope, priceConverter) {
        if (!this._data?.currentDayProfile) {
            // No current day profile available
            return;
        }
        
        const profile = this._data.currentDayProfile.getProfile();
        if (!profile || profile.length === 0) {
            // No profile data for current day
            return;
        }

        const ctx = scope.context;
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const { mediaSize } = scope;
        
        // Ensure we have valid media size
        if (!mediaSize || mediaSize.width <= 0) {
            return;
        }
        
        ctx.save();
        
        try {
            ctx.imageSmoothingEnabled = false;
            ctx.textBaseline = 'middle';
            
            // Calculate side profile positioning
            const chartWidth = mediaSize.width;
            const profileWidth = this._data.sideProfileWidth || 100;
            const rightMargin = 1;
            const profileStartX = chartWidth - profileWidth - rightMargin;
            
            this._drawCurrentDaySideProfile(ctx, scope, profile, profileStartX, profileWidth);
            
        } finally {
            ctx.restore();
        }
    }

    _drawCurrentDaySideProfile(ctx, scope, profile, startX, maxWidth) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const series = this._data.series;
        if (!series) return;
        
        // Find max values for normalization
        let maxVolume = 0;
        let maxAbsDelta = 0;
        profile.forEach(dataPoint => {
            maxVolume = Math.max(maxVolume, dataPoint.totalVolume);
            maxAbsDelta = Math.max(maxAbsDelta, Math.abs(dataPoint.delta));
        });
        
        if (maxVolume === 0) return;
        
        // Draw each price level
        profile.forEach((dataPoint, index) => {
            const yTop = series.priceToCoordinate(dataPoint.priceLevelHigh);
            const yBottom = series.priceToCoordinate(dataPoint.priceLevelLow);
            
            if (yTop === null || yBottom === null) return;
            
            const barHeight = Math.abs(yBottom - yTop);
            const barY = Math.min(yTop, yBottom);
            
            // Calculate bar widths based on volume percentage
            const volumePercent = dataPoint.totalVolume / maxVolume;
            const deltaPercent = maxAbsDelta > 0 ? Math.abs(dataPoint.delta) / maxAbsDelta : 0;
            
            const volumeBarWidth = volumePercent * maxWidth * 0.8; // 80% for volume
            const deltaBarWidth = deltaPercent * maxWidth * 0.8; // 80% for delta
            
            // Position bars pointing from right to left
            const rightEdge = startX + maxWidth;
            
            let volumeBarData = null;
            let deltaBarData = null;
            
            if (this._data.sideShowValues && volumeBarWidth > 0.5) {
                // Draw volume bar - pointing from right to left
                // Use the EXACT same volume color logic as main profile
                const volumeColor = this._getMainProfileVolumeColor(dataPoint.volumePercent);
                const volumeStartX = rightEdge - volumeBarWidth;
                
                volumeBarData = {
                    x: volumeStartX,
                    y: barY,
                    width: volumeBarWidth,
                    height: barHeight
                };
                
                this._drawSideBarNoBorder(ctx, scope, 
                    volumeStartX, barY, 
                    volumeBarWidth, barHeight, 
                    volumeColor
                );
                
                // Volume text removed as requested
            }
            
            if (this._data.sideShowDelta && dataPoint.delta !== 0) {
                // Draw delta bar on top of volume bar - pointing from right to left
                // Use the EXACT same delta color logic as main profile
                const deltaColor = dataPoint.isDeltaPositive ? '#26a69a' : '#ef5350';
                // Ensure minimum visible width for very small deltas
                const minVisibleDeltaWidth = 1; // 1 pixel minimum
                const actualDeltaWidth = Math.max(deltaBarWidth, minVisibleDeltaWidth);
                const deltaStartX = rightEdge - volumeBarWidth - actualDeltaWidth; // No gap - adjacent bars
                
                deltaBarData = {
                    x: deltaStartX,
                    y: barY,
                    width: actualDeltaWidth,
                    height: barHeight
                };
                
                this._drawSideBarNoBorder(ctx, scope,
                    deltaStartX, barY,
                    actualDeltaWidth, barHeight,
                    deltaColor
                );
                
                // Delta text labels hidden as requested
                // this._drawDeltaText(ctx, scope,
                //     rightEdge, barY + barHeight / 2,
                //     this._formatDelta(dataPoint.delta)
                // );
            }
            
            // Draw custom borders for this bar set
            this._drawSideBarBorders(ctx, scope, volumeBarData, deltaBarData);
            
            // Price labels removed as requested
        });
        
        // Draw POC, VAH, VAL lines if enabled (same as main profile)
        if (this._data.showPOC || this._data.showValueArea) {
            const dailyProfile = this._data.currentDayProfile;
            this._drawSideProfileMarketLines(ctx, scope, dailyProfile, series, startX, startX + maxWidth);
        }
    }
    
    _drawSideBar(ctx, scope, x, y, width, height, color, opacity) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        const xBitmap = Math.round(x * h);
        const yBitmap = Math.round(y * v);
        const widthBitmap = Math.max(1, Math.round(width * h));
        const heightBitmap = Math.max(1, Math.round(height * v));
        
        ctx.save();
        ctx.globalAlpha = opacity;
        ctx.fillStyle = color;
        ctx.fillRect(xBitmap, yBitmap, widthBitmap, heightBitmap);
        
        // Add subtle border
        ctx.globalAlpha = opacity * 0.3;
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        ctx.strokeRect(xBitmap, yBitmap, widthBitmap, heightBitmap);
        ctx.restore();
    }
    
    /**
     * Draw side bar using the exact same style as main profile
     */
    _drawSideBarWithMainProfileStyle(ctx, scope, x, y, width, height, color) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        // Convert to bitmap coordinates (same logic as main profile)
        const xBitmap = Math.round(x * h);
        const yBitmap = Math.round(y * v);
        const widthBitmap = Math.max(1, Math.round(width * h));
        const heightBitmap = Math.max(1, Math.round(height * v));
        
        ctx.save();
        
        try {
            // Fill with reduced opacity for side profile (85% of main profile opacity)
            const fillAlpha = (typeof this._data?.profileFillOpacity === 'number' ? this._data.profileFillOpacity : 0.1) * 0.85;
            ctx.fillStyle = this._makeTransparent(color, Math.max(0, Math.min(1, fillAlpha)));
            ctx.fillRect(xBitmap, yBitmap, widthBitmap, heightBitmap);
            
            // Border with reduced opacity and width for side profile
            const borderAlpha = (typeof this._data?.profileBorderOpacity === 'number' ? this._data.profileBorderOpacity : 0.16) * 0.4; // Reduce border opacity by 60%
            ctx.strokeStyle = this._makeTransparent(this._data?.borderColor || '#FFFFFF', borderAlpha);
            ctx.lineWidth = 0.3; // Reduce border width from 0.5 to 0.3
            ctx.strokeRect(xBitmap, yBitmap, widthBitmap, heightBitmap);
            
        } finally {
            ctx.restore();
        }
    }

    /**
     * Draw side bar without border (for custom border drawing)
     */
    _drawSideBarNoBorder(ctx, scope, x, y, width, height, color) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        // Convert to bitmap coordinates (same logic as main profile)
        const xBitmap = Math.round(x * h);
        const yBitmap = Math.round(y * v);
        const widthBitmap = Math.max(1, Math.round(width * h));
        const heightBitmap = Math.max(1, Math.round(height * v));
        
        ctx.save();
        
        try {
            // Fill with reduced opacity for side profile (85% of main profile opacity)
            const fillAlpha = (typeof this._data?.profileFillOpacity === 'number' ? this._data.profileFillOpacity : 0.1) * 0.85;
            ctx.fillStyle = this._makeTransparent(color, Math.max(0, Math.min(1, fillAlpha)));
            ctx.fillRect(xBitmap, yBitmap, widthBitmap, heightBitmap);
            
        } finally {
            ctx.restore();
        }
    }

    /**
     * Draw selective borders for side profile bars
     */
    _drawSideBarBorders(ctx, scope, volumeBarData, deltaBarData) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        // Border settings
        const borderAlpha = (typeof this._data?.profileBorderOpacity === 'number' ? this._data.profileBorderOpacity : 0.16) * 0.4;
        const borderColor = this._makeTransparent(this._data?.borderColor || '#FFFFFF', borderAlpha);
        const lineWidth = 0.3;
        
        ctx.save();
        try {
            ctx.strokeStyle = borderColor;
            ctx.lineWidth = lineWidth;
            ctx.setLineDash([]);
            ctx.beginPath();
            
            if (volumeBarData && deltaBarData) {
                // Both bars exist - draw outer borders and center divider
                const leftX = Math.round(deltaBarData.x * h) + 0.5;
                const rightX = Math.round((volumeBarData.x + volumeBarData.width) * h) + 0.5;
                const centerX = Math.round((volumeBarData.x) * h) + 0.5;
                const topY = Math.round(volumeBarData.y * v) + 0.5;
                const bottomY = Math.round((volumeBarData.y + volumeBarData.height) * v) + 0.5;
                
                // Left border (delta side)
                ctx.moveTo(leftX, topY);
                ctx.lineTo(leftX, bottomY);
                
                // Top border
                ctx.moveTo(leftX, topY);
                ctx.lineTo(rightX, topY);
                
                // Right border (volume side)
                ctx.moveTo(rightX, topY);
                ctx.lineTo(rightX, bottomY);
                
                // Bottom border
                ctx.moveTo(leftX, bottomY);
                ctx.lineTo(rightX, bottomY);
                
                // Center divider between volume and delta
                ctx.moveTo(centerX, topY);
                ctx.lineTo(centerX, bottomY);
                
            } else if (volumeBarData) {
                // Only volume bar - draw full border
                const x = Math.round(volumeBarData.x * h) + 0.5;
                const y = Math.round(volumeBarData.y * v) + 0.5;
                const width = Math.round(volumeBarData.width * h);
                const height = Math.round(volumeBarData.height * v);
                ctx.rect(x, y, width, height);
                
            } else if (deltaBarData) {
                // Only delta bar - draw full border
                const x = Math.round(deltaBarData.x * h) + 0.5;
                const y = Math.round(deltaBarData.y * v) + 0.5;
                const width = Math.round(deltaBarData.width * h);
                const height = Math.round(deltaBarData.height * v);
                ctx.rect(x, y, width, height);
            }
            
            ctx.stroke();
            
        } finally {
            ctx.restore();
        }
    }
    
    /**
     * Get volume color using same logic as main profile
     */
    _getMainProfileVolumeColor(volumePercent) {
        if (volumePercent > 0.73) {
            return '#ff9800'; // High volume nodes (orange)
        } else if (volumePercent < 0.21) {
            return '#ef5350'; // Low volume nodes (red) - brighter for visibility
        } else {
            return '#2196f3'; // Average volume nodes (blue)
        }
    }
    
    _drawDeltaText(ctx, scope, x, y, text) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        ctx.save();
        ctx.font = this._volumeFont;
        ctx.fillStyle = '#FFFFFF';
        ctx.textAlign = 'right';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x * h, y * v);
        ctx.restore();
    }
    
    _getVolumeColor(buyVolume, sellVolume) {
        if (buyVolume > sellVolume) {
            return this._data.buyColor || 'rgba(76, 175, 80, 0.8)';
        } else if (sellVolume > buyVolume) {
            return this._data.sellColor || 'rgba(244, 67, 54, 0.8)';
        } else {
            return this._data.neutralColor || 'rgba(158, 158, 158, 0.8)';
        }
    }
    
    // Helper to make colors transparent (same as main profile)
    _makeTransparent(color, alpha) {
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }
        return color;
    }
    
    _formatVolume(volume) {
        if (volume >= 1000000) {
            return (volume / 1000000).toFixed(1) + 'M';
        } else if (volume >= 1000) {
            return (volume / 1000).toFixed(1) + 'K';
        }
        return volume.toString();
    }
    
    _formatDelta(delta) {
        const sign = delta >= 0 ? '+' : '';
        if (Math.abs(delta) >= 1000000) {
            return sign + (delta / 1000000).toFixed(1) + 'M';
        } else if (Math.abs(delta) >= 1000) {
            return sign + (delta / 1000).toFixed(1) + 'K';
        }
        return sign + delta.toString();
    }
    
    /**
     * Draw POC, VAH, VAL lines for side profile (same as main profile)
     */
    _drawSideProfileMarketLines(ctx, scope, dailyProfile, series, profileLeftX, profileRightX) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        // Get POC and Value Area
        const poc = dailyProfile.getPOC();
        const valueArea = dailyProfile.getValueArea();
        
        if (!poc || !valueArea) return;
        
        // Use profile boundaries to keep lines contained within the profile area
        const lineStartX = profileLeftX;
        const lineEndX = profileRightX;
        
        ctx.save();
        
        try {
            // All market profile lines use consistent width and dashed style
            const lineStyle = {
                width: this._data.lineWidth || 1.5,
                style: 'dashed'
            };
            
            // Draw POC line if enabled
            if (this._data.showPOC) {
                const pocY = series.priceToCoordinate(poc.price);
                if (pocY !== null) {
                    this._drawSideHorizontalLine(ctx, scope, lineStartX, lineEndX, pocY, {
                        color: this._data.pocColor || '#FFFFFF',
                        ...lineStyle
                    });
                }
            }
            
            // Draw VAH and VAL lines if enabled
            if (this._data.showValueArea) {
                // Draw VAH line (up color)
                const vahY = series.priceToCoordinate(valueArea.high);
                if (vahY !== null) {
                    this._drawSideHorizontalLine(ctx, scope, lineStartX, lineEndX, vahY, {
                        color: this._data.vahColor || '#26a69a',
                        ...lineStyle
                    });
                }
                
                // Draw VAL line (down color)
                const valY = series.priceToCoordinate(valueArea.low);
                if (valY !== null) {
                    this._drawSideHorizontalLine(ctx, scope, lineStartX, lineEndX, valY, {
                        color: this._data.valColor || '#ef5350',
                        ...lineStyle
                    });
                }
            }
            
        } finally {
            ctx.restore();
        }
    }
    
    /**
     * Draw horizontal line for side profile with pixel-perfect rendering
     */
    _drawSideHorizontalLine(ctx, scope, startX, endX, y, options) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        // Convert to bitmap coordinates
        const x1 = Math.round(startX * h);
        const x2 = Math.round(endX * h);
        const yPos = Math.round(y * v) + 0.5; // Add 0.5 for crisp 1px lines
        
        ctx.strokeStyle = options.color;
        ctx.lineWidth = options.width;
        
        // Set line style
        if (options.style === 'dashed') {
            ctx.setLineDash([5, 3]);
        } else {
            ctx.setLineDash([]);
        }
        
        ctx.beginPath();
        ctx.moveTo(x1, yPos);
        ctx.lineTo(x2, yPos);
        ctx.stroke();
    }
}

/**
 * Volume Profile Renderer - Center Display
 */
class VolumeProfileRenderer {
    constructor(data) {
        this._data = data;
        this._volumeFont = '7px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
        this._priceFont = '8px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
    }

    draw(target, priceConverter) {
        // If disabled upstream, renderer data may include a flag to skip drawing
        if (!this._data || (this._data && this._data._disabled)) return;
        target.useBitmapCoordinateSpace(scope => {
            if (this._data && !this._data._disabled) {
                this._drawImpl(scope, priceConverter);
            }
        });
    }

    _drawImpl(scope, priceConverter) {
        if (!this._data?.dailyProfiles || this._data.dailyProfiles.length === 0) {
            return;
        }

        const ctx = scope.context;
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;

        // Save context state (useBitmapCoordinateSpace handles this automatically, but for safety)
        ctx.save();
        
        try {
            // Set optimized canvas state for crisp rendering
            ctx.imageSmoothingEnabled = false;
            ctx.textBaseline = 'middle';
            ctx.textAlign = 'center';
            
            // Draw each daily volume profile with optimized batching
            this._data.dailyProfiles.forEach(dailyProfile => {
                // Skip profiles with no data to avoid unnecessary work
                const profile = dailyProfile.getProfile();
                if (!profile || profile.length === 0) return;
                
                this._drawDailyProfile(ctx, scope, dailyProfile, this._data.series);
            });
            
        } finally {
            // Restore context state
            ctx.restore();
        }
    }

    _drawDailyProfile(ctx, scope, dailyProfile, series) {
        const profile = dailyProfile.getProfile();
        if (!profile || profile.length === 0 || !series) {
            return;
        }

        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        // Stabilized center positioning to prevent flickering during pan
        let centerX = this._getStableCenterX(dailyProfile, scope);
        
        if (centerX === null) {
            return; // Skip drawing if we can't determine a stable position
        }
        
        // Calculate day span in pixels for proper 50% width sizing
        const dayStartX = this._data.timeScale.timeToCoordinate(dailyProfile.startTime);
        const dayEndX = this._data.timeScale.timeToCoordinate(dailyProfile.endTime);
        
        // Calculate actual day width and set profile to 50% of day width
        let dayWidth;
        if (dayStartX !== null && dayEndX !== null) {
            dayWidth = Math.abs(dayEndX - dayStartX);
        } else {
            // Fallback width calculation based on visible range and time span
            const vr = this._data.timeScale.getVisibleRange?.();
            if (vr?.from != null && vr?.to != null) {
                const timeSpan = dailyProfile.endTime - dailyProfile.startTime;
                const visibleTimeSpan = vr.to - vr.from;
                const visibleWidth = scope.mediaSize?.width || 800;
                dayWidth = (timeSpan / visibleTimeSpan) * visibleWidth;
            } else {
                dayWidth = 200; // Reasonable fallback
            }
        }
        // If day width is extremely small (e.g., only one bar visible), ensure a minimal width so it doesn't vanish
        const MIN_PROFILE_WIDTH_PX = 24; // visible but unobtrusive
        let profileMaxWidth = dayWidth * 0.5; // 50% of day width
        if (!isFinite(profileMaxWidth) || profileMaxWidth <= 0) {
            profileMaxWidth = MIN_PROFILE_WIDTH_PX;
        } else if (profileMaxWidth < MIN_PROFILE_WIDTH_PX) {
            profileMaxWidth = MIN_PROFILE_WIDTH_PX;
        }
        const profileWidthPercent = 0.8; // Use 80% of the allocated 50% for better spacing
        
    const deltaWidthBoost = Math.max(0, Math.min(1, this._data.deltaWidthBoost ?? 0));

    profile.forEach(dataPoint => {
            // Use exact price level bounds from Pine Script methodology
            const yTop = series.priceToCoordinate(dataPoint.priceLevelHigh);
            const yBottom = series.priceToCoordinate(dataPoint.priceLevelLow);
            
            if (yTop === null || yBottom === null) return;
            
            // Calculate bar dimensions - fill the entire price level range
            const barHeight = Math.abs(yBottom - yTop);
            const barY = Math.min(yTop, yBottom);
            
            // Volume bar positioning (right side from center)
            const volumeBarWidth = dataPoint.volumePercent * profileMaxWidth * profileWidthPercent;
            const volumeStartX = centerX;
            const volumeEndX = centerX + volumeBarWidth;
            
            // Delta bar positioning (left side from center)  
            let deltaBarWidth = dataPoint.deltaPercent * profileMaxWidth * profileWidthPercent;
            // Apply slight width boost for visibility
            if (deltaBarWidth > 0) {
                deltaBarWidth *= (1 + deltaWidthBoost);
                // Clamp to allocated space
                deltaBarWidth = Math.min(deltaBarWidth, profileMaxWidth * profileWidthPercent);
                // Ensure a minimal visible width for very tiny deltas
                const MIN_DELTA_BAR_PX = 1.5;
                if (deltaBarWidth > 0 && deltaBarWidth < MIN_DELTA_BAR_PX) {
                    deltaBarWidth = MIN_DELTA_BAR_PX;
                }
            }
            const deltaStartX = centerX - deltaBarWidth;
            const deltaEndX = centerX;
            
            // Determine if either side is effectively zero width
            const VISIBLE_THRESHOLD = 0.5;
            const isVolumeTiny = volumeBarWidth <= VISIBLE_THRESHOLD;
            const isDeltaTiny = deltaBarWidth <= VISIBLE_THRESHOLD;

            // Draw volume bar (right side) only if visibly wide enough; otherwise we'll draw a center line
            if (!isVolumeTiny) {
                this._drawPixelPerfectProfileBox(ctx, scope, 
                    volumeStartX / h, barY / v, 
                    volumeEndX / h, (barY + barHeight) / v,
                    this._getVolumeColor(dataPoint.volumePercent)
                );
            }
            
            // Draw delta bar (left side) with pixel-perfect rendering
            if (!isDeltaTiny) { // Only draw if visible
                this._drawPixelPerfectProfileBox(ctx, scope,
                    deltaStartX / h, barY / v,
                    deltaEndX / h, (barY + barHeight) / v,
                    dataPoint.isDeltaPositive ? '#26a69a' : '#ef5350'
                );
            }

            // If either side is tiny (0 volume or near-0 delta), draw a single vertical line at center
            if (isVolumeTiny || isDeltaTiny) {
                this._drawVerticalLine(ctx, scope, centerX, barY, barY + barHeight, {
                    color: this._data.gapLineColor || this._data.borderColor || '#B2B5BE',
                    width: this._data.gapLineWidth || 1
                });
            }
        });
        
        // Draw POC, VAH, VAL lines if enabled
        if (this._data.showPOC || this._data.showValueArea) {
            // Calculate profile boundaries for line constraints
            const profileLeftX = centerX - (profileMaxWidth * profileWidthPercent);
            const profileRightX = centerX + (profileMaxWidth * profileWidthPercent);
            this._drawMarketProfileLines(ctx, scope, dailyProfile, series, profileLeftX, profileRightX);
        }
    }

    /**
     * Get stable center X coordinate that doesn't flicker during pan operations
     */
    _getStableCenterX(dailyProfile, scope) {
        // Use a more stable positioning approach
        const timeScale = this._data.timeScale;
        
        // Try multiple positioning strategies in order of preference
        
        // Strategy 1: Use the day's center time if it's visible
        let centerX = timeScale.timeToCoordinate(dailyProfile.centerTime);
        if (centerX !== null) {
            return centerX;
        }
        
        // Strategy 2: Find the intersection of the day with the visible range
        const vr = timeScale.getVisibleRange?.();
        if (vr?.from != null && vr?.to != null) {
            const interStart = Math.max(dailyProfile.startTime, vr.from);
            const interEnd = Math.min(dailyProfile.endTime, vr.to);
            
            if (interStart <= interEnd) {
                // Use the middle of the visible intersection
                const intersectionMid = interStart + (interEnd - interStart) / 2;
                centerX = timeScale.timeToCoordinate(intersectionMid);
                if (centerX !== null) {
                    return centerX;
                }
            }
        }
        
        // Strategy 3: Use boundary coordinates if available
        const dayStartX = timeScale.timeToCoordinate(dailyProfile.startTime);
        const dayEndX = timeScale.timeToCoordinate(dailyProfile.endTime);
        
        if (dayStartX !== null && dayEndX !== null) {
            return (dayStartX + dayEndX) / 2;
        } else if (dayStartX !== null) {
            return dayStartX;
        } else if (dayEndX !== null) {
            return dayEndX;
        }
        
        // Strategy 4: Last resort - use logical positioning
        if (vr?.from != null && vr?.to != null) {
            const visibleLogicalRange = timeScale.getVisibleLogicalRange?.();
            if (visibleLogicalRange) {
                const chartWidth = scope?.mediaSize?.width || 800;
                const barsVisible = visibleLogicalRange.to - visibleLogicalRange.from;
                const barSpacing = chartWidth / barsVisible;
                
                // Estimate position based on time relationship
                const dayMidTime = dailyProfile.centerTime;
                const relativePosition = (dayMidTime - vr.from) / (vr.to - vr.from);
                
                if (relativePosition >= 0 && relativePosition <= 1) {
                    return relativePosition * chartWidth;
                }
            }
        }
        
        return null; // Unable to determine stable position
    }

    /**
     * Draw vertical line with pixel-perfect rendering
     */
    _drawVerticalLine(ctx, scope, x, y1, y2, options) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        // Convert to bitmap coordinates
        const xPos = Math.round(x * h) + 0.5; // 0.5 for crisp 1px lines
        const yStart = Math.round(y1 * v);
        const yEnd = Math.round(y2 * v);

        ctx.save();
        try {
            ctx.strokeStyle = options.color;
            ctx.lineWidth = options.width ?? 1;
            ctx.setLineDash([]);
            ctx.beginPath();
            ctx.moveTo(xPos, yStart);
            ctx.lineTo(xPos, yEnd);
            ctx.stroke();
        } finally {
            ctx.restore();
        }
    }

    /**
     * Pixel-perfect profile box drawing using TradingView best practices
     * Draws shapes using proper bitmap coordinate calculations
     */
    _drawPixelPerfectProfileBox(ctx, scope, x1Media, y1Media, x2Media, y2Media, color) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        // Use TradingView's pixel-perfect coordinate calculation
        const xPos = positionsBoxPerfect(x1Media, x2Media, h);
        const yPos = positionsBoxPerfect(y1Media, y2Media, v);
        
        // Ensure minimum visible dimensions
        const width = Math.max(1, xPos.length);
        const height = Math.max(1, yPos.length);
        
        // Pixel-perfect box draw debug info removed per request.
        
        // Save context state
        ctx.save();
        
        try {
            // Use integer coordinates for crisp rendering
            const x = Math.round(xPos.position);
            const y = Math.round(yPos.position);
            
            // Fill with reduced opacity for subtler background rendering
            const fillAlpha = typeof this._data?.profileFillOpacity === 'number' ? this._data.profileFillOpacity : 0.1;
            ctx.fillStyle = this._makeTransparent(color, Math.max(0, Math.min(1, fillAlpha)));
            ctx.fillRect(x, y, width, height);
            
            // Add subtle border for definition
            const borderAlpha = typeof this._data?.profileBorderOpacity === 'number' ? this._data.profileBorderOpacity : 0.16;
            ctx.strokeStyle = this._makeTransparent(color, Math.max(0, Math.min(1, borderAlpha)));
            ctx.lineWidth = this._data?.lineWidth || 1.5;
            ctx.strokeRect(x + 0.5, y + 0.5, width - 1, height - 1);
            
        } finally {
            ctx.restore();
        }
    }
    
    // Helper to make colors transparent
    _makeTransparent(color, alpha) {
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }
        return color;
    }

    /**
     * Draw POC, VAH, VAL lines for market profile
     */
    _drawMarketProfileLines(ctx, scope, dailyProfile, series, profileLeftX, profileRightX) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        // Get POC and Value Area
        const poc = dailyProfile.getPOC();
        const valueArea = dailyProfile.getValueArea();
        
        if (!poc || !valueArea) return;
        
        // Use profile boundaries to keep lines contained within the profile area
        const lineStartX = profileLeftX;
        const lineEndX = profileRightX;
        
        ctx.save();
        
        try {
            // All market profile lines use consistent width and dashed style
            const lineStyle = {
                width: this._data.lineWidth,
                style: 'dashed'
            };
            
            // Draw POC line if enabled
            if (this._data.showPOC) {
                const pocY = series.priceToCoordinate(poc.price);
                if (pocY !== null) {
                    this._drawHorizontalLine(ctx, scope, lineStartX, lineEndX, pocY, {
                        color: this._data.pocColor,
                        ...lineStyle
                    });
                }
            }
            
            // Draw VAH and VAL lines if enabled
            if (this._data.showValueArea) {
                // Draw VAH line (up color)
                const vahY = series.priceToCoordinate(valueArea.high);
                if (vahY !== null) {
                    this._drawHorizontalLine(ctx, scope, lineStartX, lineEndX, vahY, {
                        color: this._data.vahColor,
                        ...lineStyle
                    });
                }
                
                // Draw VAL line (down color)
                const valY = series.priceToCoordinate(valueArea.low);
                if (valY !== null) {
                    this._drawHorizontalLine(ctx, scope, lineStartX, lineEndX, valY, {
                        color: this._data.valColor,
                        ...lineStyle
                    });
                }
            }
            
        } finally {
            ctx.restore();
        }
    }
    
    /**
     * Draw horizontal line with pixel-perfect rendering
     */
    _drawHorizontalLine(ctx, scope, startX, endX, y, options) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        // Convert to bitmap coordinates
        const x1 = Math.round(startX * h);
        const x2 = Math.round(endX * h);
        const yPos = Math.round(y * v) + 0.5; // Add 0.5 for crisp 1px lines
        
        ctx.strokeStyle = options.color;
        ctx.lineWidth = options.width;
        
        // Set line style
        if (options.style === 'dashed') {
            ctx.setLineDash([5, 3]);
        } else {
            ctx.setLineDash([]);
        }
        
        ctx.beginPath();
        ctx.moveTo(x1, yPos);
        ctx.lineTo(x2, yPos);
        ctx.stroke();
    }
    
    // Get volume color with better contrast for background rendering
    _getVolumeColor(volumePercent) {
        if (volumePercent > 0.73) {
            return '#ff9800'; // High volume nodes (orange)
        } else if (volumePercent < 0.21) {
            return '#ef5350'; // Low volume nodes (red) - brighter for visibility
        } else {
            return '#2196f3'; // Average volume nodes (blue)
        }
    }
}

/**
 * Volume Profile Pane View - Center Display
 */
class VolumeProfilePaneView {
    constructor(source) {
        this._source = source;
        this._renderer = new VolumeProfileRenderer({});
        this._sideRenderer = new SideVolumeProfileRenderer({});
        this._cachedData = null;
    }

    update() {
        const dataManager = this._source._dataManager;
        const dailyProfiles = dataManager.getAllDailyProfiles();
        
        // Check if footprint cells are visible and auto-toggle main profile
        const footprintCellsVisible = this._checkFootprintCellsVisible();
        
        // Auto-hide main profile when footprint cells are visible
        // Auto-show main profile when footprint cells are hidden (but only if not manually disabled)
        if (footprintCellsVisible && this._source._options.enabled) {
            // Auto-hiding main profile: footprint cells visible
            this._source._options.enabled = false;
            this._source._options._autoHidden = true; // Mark as auto-hidden
        } else if (!footprintCellsVisible && !this._source._options.enabled && this._source._options._autoHidden && !this._source._options._manuallyDisabled) {
            // Auto-showing main profile: footprint cells hidden
            this._source._options.enabled = true;
            this._source._options._autoHidden = false; // Clear auto-hidden flag
        }
        
        // Check if either main profile or side profile is enabled
        const mainProfileEnabled = this._source._options.enabled;
        const sideProfileEnabled = this._source._options.showSideProfile;
        
        if (!mainProfileEnabled && !sideProfileEnabled) {
            this._cachedData = null;
            return;
        }
        
        if (dailyProfiles.length === 0) {
            this._cachedData = null;
            return;
        }

        // Get time scale from chart
        const timeScale = this._source._chart?.timeScale();
        if (!timeScale) {
            this._cachedData = null;
            return;
        }

        // Get series for price conversion
        const series = this._source._series;
        if (!series) {
            this._cachedData = null;
            return;
        }

        // Find current day profile
        const currentDayProfile = this._getCurrentDayProfile(dailyProfiles);

        // Check if we need to update cache (avoid redundant updates)
        const currentHash = this._calculateDataHash(dailyProfiles, this._source._options);
        if (this._lastDataHash === currentHash && this._cachedData) {
            return; // No update needed
        }
        
        this._lastDataHash = currentHash;
        
        // Pre-calculate commonly used values
        const options = this._source._options;
        this._cachedData = {
            dailyProfiles: dailyProfiles,
            currentDayProfile: currentDayProfile,
            timeScale: timeScale,
            series: series,
            maxBarWidth: options.maxBarWidth || 80,
            barHeight: options.barHeight || 4,
            deltaWidthBoost: options.deltaWidthBoost ?? 0.15,
            profileFillOpacity: typeof options.profileFillOpacity === 'number' ? options.profileFillOpacity : 0.1,
            profileBorderOpacity: typeof options.profileBorderOpacity === 'number' ? options.profileBorderOpacity : 0.16,
            gapLineColor: options.gapLineColor,
            gapLineWidth: options.gapLineWidth,
            buyColor: options.buyColor,
            sellColor: options.sellColor,
            neutralColor: options.neutralColor,
            borderColor: options.borderColor,
            priceTextColor: options.priceTextColor,
            // Market Profile Lines
            showPOC: options.showPOC ?? true,
            showValueArea: options.showValueArea ?? true,
            pocColor: options.pocColor || '#FFFFFF',
            vahColor: options.vahColor || '#26a69a',
            valColor: options.valColor || '#ef5350',
            lineWidth: options.lineWidth || 1.5,
            // Side profile options
            showSideProfile: options.showSideProfile ?? false,
            sideProfileWidth: options.sideProfileWidth || 100,
            sideProfileOpacity: options.sideProfileOpacity || 0.8,
            sideShowValues: options.sideShowValues ?? true,
            sideShowDelta: options.sideShowDelta ?? true,
            // Cache pixel ratios if available
            pixelRatios: this._getPixelRatios(),
            _disabled: false
        };

        this._renderer = new VolumeProfileRenderer(this._cachedData);
        this._sideRenderer = new SideVolumeProfileRenderer(this._cachedData);
    }
    
    /**
     * Check if footprint cells are currently visible based on barSpacing
     * Footprint shows cells when barSpacing >= 18
     * This method now checks only THIS chart's footprint state, not the globally selected chart
     */
    _checkFootprintCellsVisible() {
        try {
            // Use this volume profile's own chart reference, not the global selected chart
            const chart = this._source._chart;
            if (!chart) {
                return false;
            }
            
            // Find the correct chart object in the registry by matching the chart instance
            let chartObj = null;
            if (window.chartRegistry) {
                for (const obj of window.chartRegistry) {
                    if (obj.chart === chart) {
                        chartObj = obj;
                        break;
                    }
                }
            }
            
            if (!chartObj || !chartObj.footprintSeries) {
                return false; // No matching chart object or footprint series found
            }
            
            // Check if footprint series is visible (indicating footprint mode is active)
            const footprintOptions = chartObj.footprintSeries.options();
            const footprintVisible = footprintOptions?.visible !== false;
            if (!footprintVisible) {
                return false; // Footprint is not active, so no cells to show
            }
            
            // Get visible range and calculate barSpacing for THIS specific chart
            const timeScale = chart.timeScale();
            const visibleRange = timeScale.getVisibleLogicalRange();
            if (!visibleRange) return false;
            
            const chartWidth = chart.options().width || 800;
            const barsVisible = visibleRange.to - visibleRange.from;
            const barSpacing = chartWidth / barsVisible;
            
            // Footprint shows cells when barSpacing >= 18
            return barSpacing >= 18;
            
        } catch (error) {
            console.warn('Error checking footprint cells visibility for this chart:', error);
            return false;
        }
    }
    
    /**
     * Get current day profile based on latest timestamp
     */
    _getCurrentDayProfile(dailyProfiles) {
        if (!dailyProfiles || dailyProfiles.length === 0) return null;
        
        try {
            // Sort profiles by end time and get the most recent one
            const sortedProfiles = [...dailyProfiles].sort((a, b) => b.endTime - a.endTime);
            const currentProfile = sortedProfiles[0];
            
            // Validate that the profile has data
            if (currentProfile && currentProfile.candles && currentProfile.candles.length > 0) {
                return currentProfile;
            }
        } catch (error) {
            console.warn('Error getting current day profile:', error);
        }
        
        return null;
    }
    
    /**
     * Calculate a simple hash for data change detection
     */
    _calculateDataHash(profiles, options) {
        let hash = profiles.length;
        profiles.forEach(profile => {
            hash = hash * 31 + (profile._lastCandleCount || 0);
            // Include intra-candle update versions so live updates trigger re-render
            hash = hash * 31 + (profile._updateVersion || 0);
        });
        hash = hash * 31 + (options.deltaWidthBoost * 1000 | 0);
        return hash;
    }
    
    /**
     * Get pixel ratios from chart if available
     */
    _getPixelRatios() {
        try {
            // Try to get pixel ratios from chart canvas if available
            const canvas = this._source._chart?.chartElement?.()?.querySelector('canvas');
            if (canvas) {
                const rect = canvas.getBoundingClientRect();
                return {
                    horizontal: canvas.width / rect.width,
                    vertical: canvas.height / rect.height
                };
            }
        } catch (e) {
            // Fallback to default
        }
        return { horizontal: 1, vertical: 1 };
    }

    renderer() {
        // Return composite renderer that calls both center and side renderers
        return {
            draw: (target, priceConverter) => {
                // Only draw if we have valid cached data
                if (!this._cachedData) {
                    return;
                }
                
                // Draw center profile if main profile is enabled
                if (this._source._options.enabled && this._renderer) {
                    this._renderer.draw(target, priceConverter);
                }
                
                // Draw side profile if side profile is enabled (independent of main profile)
                if (this._source._options.showSideProfile && this._sideRenderer) {
                    this._sideRenderer.draw(target, priceConverter);
                }
            }
        };
    }

    zOrder() {
        return 'normal'; // Use normal z-order, make transparent instead
    }
}

/**
 * Main Volume Profile Plugin Class - Center Display
 */
export class VolumeProfile {
    constructor(chart, series, options = {}) {
        this._chart = chart;
        this._series = series;
        this._dataManager = new VolumeProfileDataManager();
        this._paneViews = [new VolumeProfilePaneView(this)];
        
        // Default options for center display
        this._options = {
            maxBarWidth: 80,
            barHeight: 4,
            // Increase delta bar visibility; 0.15 => +15% width boost on normalized delta
            deltaWidthBoost: 0.15,
            // Slightly reduced default opacity for a lighter appearance
            profileFillOpacity: 0.1,
            profileBorderOpacity: 0.16,
            // Vertical line used when either side has effectively zero width
            gapLineColor: '#9aa0a6',
            gapLineWidth: 1,
            buyColor: 'rgba(76, 175, 80, 0.8)',
            sellColor: 'rgba(244, 67, 54, 0.8)',
            neutralColor: 'rgba(158, 158, 158, 0.8)',
            borderColor: 'rgba(255, 255, 255, 0.2)',
            priceTextColor: '#B2B5BE',
            tickSize: 0.05,
            enabled: true,
            // Market Profile Lines
            showPOC: true,
            showValueArea: true,
            pocColor: '#FFFFFF',
            vahColor: '#26a69a',  // Up color (green)
            valColor: '#ef5350',  // Down color (red)
            lineWidth: 1.5,
            // Side Profile Options (current day only)
            showSideProfile: false,
            sideProfileWidth: 100,
            sideProfileOpacity: 0.8,
            sideShowValues: true,
            sideShowDelta: true,
            ...options
        };

        this._dataManager.setTickSize(this._options.tickSize);
        this._isDestroyed = false;
        this._updateThrottled = false;
        this._renderThrottled = false;

        // Re-render on pan/zoom to keep coordinates fresh and avoid disappearing artifacts
        try {
            this._onTimeRangeChange = () => this.updateAllViews();
            this._chart?.timeScale()?.subscribeVisibleTimeRangeChange(this._onTimeRangeChange);
        } catch {}
    }

    // Add footprint data to volume profile with batching
    addFootprintData(candleData) {
        // Add data if either profile is enabled
        if ((!this._options.enabled && !this._options.showSideProfile) || this._isDestroyed) return;
        
        this._dataManager.addFootprintData(candleData);
        
        // Batch updates using requestAnimationFrame
        if (!this._renderThrottled) {
            this._renderThrottled = true;
            requestAnimationFrame(() => {
                this.updateAllViews();
                this._renderThrottled = false;
            });
        }
    }

    // Update all views with optimized throttling and batching
    updateAllViews() {
        if (this._isDestroyed) return;
        
        // Use more sophisticated throttling with requestIdleCallback for better performance
        if (this._updateThrottled) return;
        this._updateThrottled = true;
        
        // Prefer requestIdleCallback for non-critical updates, fallback to requestAnimationFrame
        const updateFunction = () => {
            if (this._isDestroyed) return;
            
            // Batch update all views for better performance
            const updatePromises = this._paneViews.map(view => {
                return new Promise(resolve => {
                    if (view.update) {
                        view.update();
                    }
                    resolve();
                });
            });
            
            // Wait for all updates to complete, then request chart update
            Promise.all(updatePromises).then(() => {
                if (this._requestUpdate && !this._isDestroyed) {
                    this._requestUpdate();
                }
            });
            
            this._updateThrottled = false;
        };
        
        // Use requestIdleCallback if available, otherwise fallback to requestAnimationFrame
        if (typeof requestIdleCallback !== 'undefined') {
            requestIdleCallback(updateFunction, { timeout: 16 }); // Max 16ms delay
        } else {
            requestAnimationFrame(updateFunction);
        }
    }

    // Apply new options
    applyOptions(options) {
        Object.assign(this._options, options);
        this._dataManager.setTickSize(this._options.tickSize);
        
        // Handle volumeProfileBars setting
        if (typeof options.volumeProfileBars === 'number') {
            this._dataManager.setNumberOfRows(options.volumeProfileBars);
        }
        
        // Handle valueAreaPercentage setting
        if (typeof options.valueAreaPercentage === 'number') {
            this._dataManager.setValueAreaPercentage(options.valueAreaPercentage);
        }
        
        this.updateAllViews();
    }

    // Clear all data
    clear() {
        this._dataManager.clear();
        this.updateAllViews();
    }

    // Get all daily profiles data (for external use)
    getAllProfiles() {
        return this._dataManager.getAllDailyProfiles();
    }

    // Primitive interface methods
    attached(param) {
        if (param && param.requestUpdate) {
            this._requestUpdate = param.requestUpdate;
        }
    }

    detached() {
        this._isDestroyed = true;
        try {
            if (this._onTimeRangeChange) {
                this._chart?.timeScale()?.unsubscribeVisibleTimeRangeChange(this._onTimeRangeChange);
            }
        } catch {}
    }

    paneViews() {
        // Show pane views if either main profile or side profile is enabled
        if (!this._options.enabled && !this._options.showSideProfile) return [];
        return this._paneViews;
    }

    priceAxisViews() {
        return [];
    }

    timeAxisViews() {
        return [];
    }

    autoscaleInfo() {
        // Don't affect autoscale since we're overlay
        return null;
    }

    // Enable/disable the volume profile
    setEnabled(enabled) {
        this._options.enabled = enabled;
        
        // Track manual disable/enable to prevent auto-show when manually disabled
        if (!enabled) {
            this._options._manuallyDisabled = true;
            this._options._autoHidden = false; // Clear auto-hidden flag when manually disabled
        } else {
            this._options._manuallyDisabled = false;
            this._options._autoHidden = false; // Clear auto-hidden flag when manually enabled
        }
        
        if (!enabled && !this._options.showSideProfile) {
            // Only clear cached data if both profiles are disabled
            this._paneViews.forEach(view => {
                if (view._cachedData) {
                    view._cachedData = null;
                }
            });
        }
        // Force chart to re-render by requesting update after a microtask
        Promise.resolve().then(() => this.updateAllViews());
        try {
            document.dispatchEvent(new CustomEvent('volumeProfileToggled', { detail: { index: window.selectedChartIndex || 0, enabled } }));
        } catch {}
    }

    // Get enabled state
    isEnabled() {
        return this._options.enabled;
    }

    // Toggle POC line visibility
    setPOCVisible(visible) {
        this._options.showPOC = visible;
        this.updateAllViews();
    }

    // Toggle Value Area lines visibility
    setValueAreaVisible(visible) {
        this._options.showValueArea = visible;
        this.updateAllViews();
    }

    // Get POC data for a specific day
    getPOCForDay(dayKey) {
        const profile = this._dataManager.dailyProfiles.get(dayKey);
        return profile ? profile.getPOC() : null;
    }

    // Get Value Area data for a specific day
    getValueAreaForDay(dayKey) {
        const profile = this._dataManager.dailyProfiles.get(dayKey);
        return profile ? profile.getValueArea() : null;
    }

    // Toggle side profile visibility
    setSideProfileVisible(visible) {
        this._options.showSideProfile = visible;
        if (!visible && !this._options.enabled) {
            // Only clear cached data if both profiles are disabled
            this._paneViews.forEach(view => {
                if (view._cachedData) {
                    view._cachedData = null;
                }
            });
        }
        this.updateAllViews();
        // Dispatch event for external components
        try {
            document.dispatchEvent(new CustomEvent('sideVolumeProfileToggled', { 
                detail: { index: window.selectedChartIndex || 0, enabled: visible } 
            }));
        } catch {}
    }

    // Get side profile enabled state
    isSideProfileEnabled() {
        return this._options.showSideProfile;
    }

    // Configure side profile options
    setSideProfileOptions(options) {
        Object.assign(this._options, {
            sideProfileWidth: options.width,
            sideProfileOpacity: options.opacity,
            sideShowValues: options.showValues,
            sideShowDelta: options.showDelta
        });
        this.updateAllViews();
    }
}

// Export default
export default VolumeProfile;
