/**
 * Settings management for trading platform
 * Handles chart settings persistence and application
 */

// Storage keys
const STORAGE_KEYS = {
    CHART_SETTINGS: 'chartSettings'
};

// Time formats
const TIME_FORMATS = {
    HOUR_12: '12h',
    HOUR_24: '24h'
};

// Default settings configuration (time and chart settings)
const DEFAULT_SETTINGS = {
    timezone: 'Asia/Kolkata', // Indian Standard Time (UTC+5:30)
    timeFormat: TIME_FORMATS.HOUR_24,
    // Chart threshold settings
    imbalanceThreshold: 300,
    severeImbalanceThreshold: 500,
    imbalanceVolumeThreshold: 2000,
    lowVolumeThreshold: 99,
    showLowVolume: true,
    showCOT: true,
    showAbsorption: true,
    volumeDisplayMode: 'split',
    // Volume Profile settings
    volumeProfileEnabled: true,
    volumeProfileBars: 24,
    valueAreaPercentage: 70,
    // Absorption Detection settings
    absorptionVolumeThreshold: 5000,
    absorptionPriceThreshold: 2 // percentage
};

// Current settings cache (loaded from localStorage or defaults)
let currentSettings = { ...DEFAULT_SETTINGS };

/**
 * Timezone conversion utilities
 * Note: For TradingView Lightweight Charts, timestamps are handled internally
 */

// ...existing code...

/**
 * Get current timezone setting
 * @returns {string} Current timezone
 */
export function getCurrentTimezone() {
    return currentSettings.timezone || 'Asia/Kolkata';
}

/**
 * Get current time format setting
 * @returns {string} Current time format
 */
export function getTimeFormat() {
    return currentSettings.timeFormat || TIME_FORMATS.HOUR_24;
}

/**
 * Storage utilities for settings persistence
 */

/**
 * Load settings from localStorage
 * @returns {object} Loaded settings object
 */
export function loadSettings() {
    try {
        const saved = localStorage.getItem(STORAGE_KEYS.CHART_SETTINGS);
        if (saved) {
            const parsedSettings = JSON.parse(saved);
            // Merge with defaults to ensure all required properties exist
            currentSettings = { ...DEFAULT_SETTINGS, ...parsedSettings };
            // Settings loaded from localStorage
        } else {
            currentSettings = { ...DEFAULT_SETTINGS };
            // Using default settings
        }
    } catch (error) {
        console.warn('Failed to load settings, using defaults:', error);
        currentSettings = { ...DEFAULT_SETTINGS };
    }
    return currentSettings;
}

/**
 * Save settings to localStorage and dispatch update event
 * @param {object} settings - Settings object to save
 * @returns {boolean} Success status
 */
export function saveSettings(settings) {
    try {
        // Merge with current settings to preserve any existing values
        const previousSettings = { ...currentSettings };
        currentSettings = { ...DEFAULT_SETTINGS, ...currentSettings, ...settings };
        
        // Save to localStorage
        localStorage.setItem(STORAGE_KEYS.CHART_SETTINGS, JSON.stringify(currentSettings));
        
        // Log settings change for debugging
        // Settings updated - previous, current, and changed values
        
        // Dispatch settings changed event immediately
        dispatchSettingsAppliedEvent(currentSettings);
        
        return true;
    } catch (error) {
        console.error('Failed to save settings:', error);
        return false;
    }
}

/**
 * Get current settings (returns a copy to prevent mutation)
 * @returns {object} Current settings object
 */
export function getSettings() {
    return { ...currentSettings };
}

/**
 * Reset settings to defaults and clear localStorage
 * @returns {object} Default settings object
 */
export function resetSettings() {
    currentSettings = { ...DEFAULT_SETTINGS };
    
    try {
        localStorage.removeItem(STORAGE_KEYS.CHART_SETTINGS);
    } catch (error) {
        console.warn('Failed to clear settings from localStorage:', error);
    }
    
    dispatchSettingsAppliedEvent(currentSettings);
    
    return currentSettings;
}

/**
 * Helper function to dispatch settings applied event
 * @param {object} settings - Settings to include in event
 */
function dispatchSettingsAppliedEvent(settings) {
    document.dispatchEvent(new CustomEvent('settingsApplied', {
        detail: settings,
        bubbles: true
    }));
}

// ...removed chart settings application utilities (not needed for time-only settings)...

/**
 * Settings system initialization and event handling
 */

/**
 * Set up event listeners for settings system
 */
function setupSettingsEventListeners() {
    // Listen for settings changes from popup
    document.addEventListener('settingsChanged', (event) => {
        const newSettings = event.detail;
        if (saveSettings(newSettings)) {
            // Settings saved and applied
        }
    });
    
    // Listen for settings applied event to update charts
    document.addEventListener('settingsApplied', (event) => {
        // Settings applied event received
        // This will be handled by the main chart application
        // The event bubbles up so chart application can listen to it
    });
}

/**
 * Initialize settings system
 * @returns {object} Loaded settings
 */
export function initializeSettings() {
    // Load saved settings first
    const settings = loadSettings();
    
    // Set up event listeners
    setupSettingsEventListeners();
    
    // Auto-apply settings on page load
    setTimeout(() => {
        dispatchSettingsAppliedEvent(settings);
    }, 100);
    
    return settings;
}

/**
 * Global API exports for settings management
 */

// Export API for global access (time-only)
window.chartSettings = {
    load: loadSettings,
    save: saveSettings,
    get: getSettings,
    reset: resetSettings,
    init: initializeSettings,
    showSettingsPopup: showSettingsPopup,
    TIME_FORMATS
};

/**
 * Settings popup utilities
 */

/**
 * Create settings popup HTML content
 * @param {object} settings - Current settings
 * @returns {string} HTML content
 */
function createSettingsPopupHTML(settings) {
    return `
        <div class="settings-header">
            <h3>Chart Settings</h3>
            <button class="close-btn" onclick="closeSettingsPopup()">×</button>
        </div>
        <div class="settings-content">
            ${createTimezoneSettingGroup(settings)}
            ${createTimeFormatSettingGroup(settings)}
            ${createThresholdSettingsGroup(settings)}
            ${createDisplaySettingsGroup(settings)}
        </div>
        <div class="settings-footer">
            <button class="reset-btn" onclick="resetSettingsToDefault()">Reset to Default</button>
            <button class="apply-btn" onclick="applySettingsFromPopup()">Apply Settings</button>
        </div>
    `;
}

/**
 * Create theme setting group HTML
 * @param {object} settings - Current settings
 * @returns {string} HTML content
 */
function createThemeSettingGroup(settings) {
    return `
        // ...removed unused create*SettingGroup functions...
                <span class="checkmark"></span>
                Show Legend
            </label>
            <div class="legend-options" style="margin-left: 24px; margin-top: 8px; ${legendOptionsStyle}">
                <label class="checkbox-wrapper" style="margin-bottom: 4px;">
                    <input type="checkbox" id="legend-symbol-toggle" ${settings.legendShowSymbol !== false ? 'checked' : ''}>
                    <span class="checkmark"></span>
                    Show Symbol
                </label>
                <label class="checkbox-wrapper" style="margin-bottom: 4px;">
                    <input type="checkbox" id="legend-ohlc-toggle" ${settings.legendShowOHLC !== false ? 'checked' : ''}>
                    <span class="checkmark"></span>
                    Show OHLC Values
                </label>
                <label class="checkbox-wrapper">
                    <input type="checkbox" id="legend-time-toggle" ${settings.legendShowTime !== false ? 'checked' : ''}>
                    <span class="checkmark"></span>
                    Show Time
                </label>
            </div>
        </div>
    `;
}

/**
 * Create timezone setting group HTML
 * @param {object} settings - Current settings
 * @returns {string} HTML content
 */
function createTimezoneSettingGroup(settings) {
    const timezones = [
        { value: 'UTC', label: 'UTC' },
        { value: 'Asia/Kolkata', label: 'Asia/Kolkata (IST)' },
        { value: 'America/New_York', label: 'America/New_York (EST/EDT)' },
        { value: 'Europe/London', label: 'Europe/London (GMT/BST)' },
        { value: 'Asia/Tokyo', label: 'Asia/Tokyo (JST)' }
    ];
    
    const options = timezones.map(tz => 
        `<option value="${tz.value}" ${settings.timezone === tz.value ? 'selected' : ''}>${tz.label}</option>`
    ).join('');
    
    return `
        <div class="setting-group">
            <label>Timezone</label>
            <select id="timezone-selector">
                ${options}
            </select>
        </div>
    `;
}

/**
 * Create time format setting group HTML
 * @param {object} settings - Current settings
 * @returns {string} HTML content
 */
function createTimeFormatSettingGroup(settings) {
    return `
        <div class="setting-group">
            <label>Time Format</label>
            <select id="time-format-selector">
                <option value="${TIME_FORMATS.HOUR_24}" ${settings.timeFormat === TIME_FORMATS.HOUR_24 ? 'selected' : ''}>24 Hour (14:30)</option>
                <option value="${TIME_FORMATS.HOUR_12}" ${settings.timeFormat === TIME_FORMATS.HOUR_12 ? 'selected' : ''}>12 Hour (2:30 PM)</option>
            </select>
        </div>
    `;
}

/**
 * Create threshold settings group HTML
 * @param {object} settings - Current settings
 * @returns {string} HTML content
 */
function createThresholdSettingsGroup(settings) {
    return `
        <div class="setting-group">
            <label style="margin-bottom: 8px; display: block; font-weight: 600; color: var(--text-primary);">Current Active Chart Settings</label>
            <div style="margin-bottom: 12px; font-size: 11px; color: var(--text-secondary); font-style: italic;">
                Values shown reflect the currently selected chart
            </div>
            <div class="threshold-row">
                <label for="imbalance-threshold-input">Imbalance (%):</label>
                <div class="controls-group">
                    <input id="imbalance-threshold-input" type="number" step="50" min="100" value="${settings.imbalanceThreshold || 300}">
                </div>
            </div>
            <div class="threshold-row">
                <label for="severe-imbalance-threshold-input">Severe Imbalance (%):</label>
                <div class="controls-group">
                    <input id="severe-imbalance-threshold-input" type="number" step="50" min="200" value="${settings.severeImbalanceThreshold || 500}">
                </div>
            </div>
            <div class="threshold-row">
                <label for="volume-threshold-input">Volume Threshold:</label>
                <div class="controls-group">
                    <input id="volume-threshold-input" type="number" step="100" min="0" value="${settings.imbalanceVolumeThreshold || 2000}">
                </div>
            </div>
            <div class="threshold-row">
                <label for="low-volume-threshold-input">Low Volume Threshold:</label>
                <div class="controls-group">
                    <input id="low-volume-threshold-input" type="number" step="10" min="10" value="${settings.lowVolumeThreshold || 99}">
                </div>
            </div>
            <div class="threshold-row">
                <label for="cot-toggle">COT Markers:</label>
                <div class="controls-group">
                    <button id="cot-toggle" class="small-toggle-btn ${settings.showCOT !== false ? 'active' : ''}" title="Toggle COT (Commitment of Traders) markers"></button>
                    <span style="font-size: 11px; color: var(--text-secondary); min-width: 60px; font-weight: 500;">High/Low</span>
                </div>
            </div>
            <div class="threshold-row">
                <label for="absorption-toggle">Absorption:</label>
                <div class="controls-group">
                    <button id="absorption-toggle" class="small-toggle-btn ${settings.showAbsorption !== false ? 'active' : ''}" title="Toggle absorption detection markers"></button>
                    <input id="absorption-volume-input" type="number" step="500" min="1000" value="${settings.absorptionVolumeThreshold || 5000}">
                </div>
            </div>
            <div class="threshold-row">
                <label for="absorption-price-input">Absorption %:</label>
                <div class="controls-group">
                    <input id="absorption-price-input" type="number" step="0.5" min="0.5" max="10" value="${settings.absorptionPriceThreshold || 2}">
                </div>
            </div>
        </div>
    `;
}

/**
 * Create display settings group HTML
 * @param {object} settings - Current settings
 * @returns {string} HTML content
 */
function createDisplaySettingsGroup(settings) {
    const chartType = settings.chartType || 'footprint';
    return `
        <div class="setting-group">
            <label style="margin-bottom: 8px; display: block; font-weight: 600; color: var(--text-primary);">Volume Profile & Display</label>
            <div style="margin-bottom: 12px; font-size: 11px; color: var(--text-secondary); font-style: italic;">
                Current active chart configuration
            </div>
            <div class="display-row">
                <label for="chart-type-select">Chart Type:</label>
                <select id="chart-type-select">
                    <option value="footprint" ${chartType === 'footprint' ? 'selected' : ''}>Footprint</option>
                    <option value="candlestick" ${chartType === 'candlestick' ? 'selected' : ''}>Candlestick</option>
                </select>
            </div>
            <div class="display-row">
                <label>Volume Profile:</label>
                <button id="chart-vp-toggle" class="toggle-btn">Toggle</button>
            </div>
            <div class="threshold-row">
                <label for="vp-bars-input">VP Bars:</label>
                <input id="vp-bars-input" type="number" step="1" min="5" max="100" value="${settings.volumeProfileBars || 24}">
            </div>
            <div class="threshold-row">
                <label for="value-area-input">Value Area %:</label>
                <input id="value-area-input" type="number" step="1" min="50" max="95" value="${settings.valueAreaPercentage || 70}">
            </div>
        </div>
    `;
}

/**
 * Position settings popup near settings button
 * @param {HTMLElement} popup - Popup element
 */
function positionSettingsPopup(popup) {
    const settingsBtn = document.getElementById('settings-btn');
    if (settingsBtn) {
        const btnRect = settingsBtn.getBoundingClientRect();
        popup.style.top = `${btnRect.bottom + 5}px`;
        popup.style.right = `${window.innerWidth - btnRect.right}px`;
    }
}

/**
 * Set up legend toggle interaction
 * @param {HTMLElement} popup - Popup element
 */
// ...removed setupLegendToggleInteraction (not needed)...

/**
 * Set up keyboard event handlers for the popup
 * @param {HTMLElement} popup - Popup element
 */
function setupKeyboardEventHandlers(popup) {
    function handleKeydown(e) {
        // Close popup on Escape key
        if (e.key === 'Escape' || e.keyCode === 27) {
            popup.remove();
            document.removeEventListener('keydown', handleKeydown);
            // Remove chart selection listener if it exists
            if (popup._chartSelectionListener) {
                document.removeEventListener('chartSelected', popup._chartSelectionListener);
            }
        }
    }
    
    // Add keyboard event listener
    document.addEventListener('keydown', handleKeydown);
    
    // Clean up event listener when popup is removed
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.removedNodes.forEach((node) => {
                if (node === popup) {
                    document.removeEventListener('keydown', handleKeydown);
                    // Remove chart selection listener if it exists
                    if (popup._chartSelectionListener) {
                        document.removeEventListener('chartSelected', popup._chartSelectionListener);
                    }
                    observer.disconnect();
                }
            });
        });
    });
    
    observer.observe(document.body, { childList: true });
}

/**
 * Update popup values with current active chart settings
 * @param {HTMLElement} popup - Popup element
 */
function updatePopupWithCurrentChartValues(popup) {
    try {
        // Get current settings (reload from localStorage to ensure freshness)
        loadSettings();
        const settings = getSettings();
        
        // Load chart-specific settings for the current chart
        const selectedIdx = window.selectedChartIndex || 0;
        const chartId = 'chart-' + selectedIdx;
        const chartObj = window.chartRegistry?.[selectedIdx];
        
        // Load saved chart-specific settings
        const chartSettings = localStorage.getItem('chartSettings_' + chartId);
        if (chartSettings) {
            const obj = JSON.parse(chartSettings);
            // Merge ALL chart-specific settings
            settings.imbalanceVolumeThreshold = obj.imbalanceVolumeThreshold || settings.imbalanceVolumeThreshold;
            settings.imbalanceThreshold = obj.imbalanceThreshold || settings.imbalanceThreshold;
            settings.severeImbalanceThreshold = obj.severeImbalanceThreshold || settings.severeImbalanceThreshold;
            settings.lowVolumeThreshold = obj.lowVolumeThreshold || settings.lowVolumeThreshold;
            // Volume Profile settings
            settings.volumeProfileBars = obj.volumeProfileBars || settings.volumeProfileBars;
            settings.valueAreaPercentage = obj.valueAreaPercentage || settings.valueAreaPercentage;
            // Absorption Detection settings
            settings.absorptionVolumeThreshold = obj.absorptionVolumeThreshold || settings.absorptionVolumeThreshold;
            settings.absorptionPriceThreshold = obj.absorptionPriceThreshold || settings.absorptionPriceThreshold;
        }
        
        // Try to get live values from chart plugins if available
        if (chartObj) {
            // Get current Volume Profile settings if plugin exists
            if (chartObj.volumeProfile && chartObj.volumeProfile._options) {
                const vpOptions = chartObj.volumeProfile._options;
                if (vpOptions.numberOfRows !== undefined) settings.volumeProfileBars = vpOptions.numberOfRows;
                if (vpOptions.valueAreaPercentage !== undefined) settings.valueAreaPercentage = vpOptions.valueAreaPercentage;
            }
            
            // Get current Footprint absorption settings if plugin exists
            if (chartObj.footprintSeries && chartObj.footprintSeries._options) {
                const fpOptions = chartObj.footprintSeries._options;
                if (fpOptions.absorptionVolumeThreshold !== undefined) settings.absorptionVolumeThreshold = fpOptions.absorptionVolumeThreshold;
                if (fpOptions.absorptionPriceThreshold !== undefined) settings.absorptionPriceThreshold = fpOptions.absorptionPriceThreshold;
                if (fpOptions.imbalanceVolumeThreshold !== undefined) settings.imbalanceVolumeThreshold = fpOptions.imbalanceVolumeThreshold;
                if (fpOptions.imbalanceThreshold !== undefined) settings.imbalanceThreshold = fpOptions.imbalanceThreshold;
                if (fpOptions.severeImbalanceThreshold !== undefined) settings.severeImbalanceThreshold = fpOptions.severeImbalanceThreshold;
                if (fpOptions.lowVolumeThreshold !== undefined) settings.lowVolumeThreshold = fpOptions.lowVolumeThreshold;
                if (fpOptions.showLowVolume !== undefined) settings.showLowVolume = fpOptions.showLowVolume;
                if (fpOptions.showCOT !== undefined) settings.showCOT = fpOptions.showCOT;
                if (fpOptions.showAbsorption !== undefined) settings.showAbsorption = fpOptions.showAbsorption;
                if (fpOptions.volumeDisplayMode !== undefined) settings.volumeDisplayMode = fpOptions.volumeDisplayMode;
            }
            
            // Set chart type if available
            if (chartObj.chartType) {
                settings.chartType = chartObj.chartType;
            }
        }
        
        // Update all input fields with new values
        const updateInput = (id, value) => {
            const input = popup.querySelector('#' + id);
            if (input) input.value = value;
        };
        
        // Update timezone and time format
        updateInput('timezone-selector', settings.timezone);
        updateInput('time-format-selector', settings.timeFormat);
        
        // Update threshold settings
        updateInput('imbalance-threshold-input', settings.imbalanceThreshold || 300);
        updateInput('severe-imbalance-threshold-input', settings.severeImbalanceThreshold || 500);
        updateInput('volume-threshold-input', settings.imbalanceVolumeThreshold || 2000);
        updateInput('low-volume-threshold-input', settings.lowVolumeThreshold || 99);
        
        // Update COT toggle button state
        const cotToggle = popup.querySelector('#cot-toggle');
        if (cotToggle) {
            const showCOT = settings.showCOT !== false; // Default to true
            if (showCOT) {
                cotToggle.classList.add('active');
            } else {
                cotToggle.classList.remove('active');
            }
        }
        
        // Update absorption toggle button state
        const absorptionToggle = popup.querySelector('#absorption-toggle');
        if (absorptionToggle) {
            const showAbsorption = settings.showAbsorption !== false; // Default to true
            if (showAbsorption) {
                absorptionToggle.classList.add('active');
            } else {
                absorptionToggle.classList.remove('active');
            }
        }
        
        // Update Volume Profile settings
        updateInput('vp-bars-input', settings.volumeProfileBars || 24);
        updateInput('value-area-input', settings.valueAreaPercentage || 70);
        
        // Update Absorption Detection settings
        updateInput('absorption-volume-input', settings.absorptionVolumeThreshold || 5000);
        updateInput('absorption-price-input', settings.absorptionPriceThreshold || 2);
        
        // Update chart type selector
        const chartTypeSelect = popup.querySelector('#chart-type-select');
        if (chartTypeSelect) {
            chartTypeSelect.value = settings.chartType || 'footprint';
        }
        
        // Updated popup with current chart values
        
    } catch (error) {
        console.warn('Error updating popup with current chart values:', error);
    }
}

/**
 * Set up chart selection change listener for the popup
 * @param {HTMLElement} popup - Popup element
 */
function setupChartSelectionListener(popup) {
    function chartSelectionListener() {
        // Chart selection changed, updating settings popup values
        updatePopupWithCurrentChartValues(popup);
    }
    
    // Add event listener for chart selection changes
    document.addEventListener('chartSelected', chartSelectionListener);
    
    // Store reference for cleanup
    popup._chartSelectionListener = chartSelectionListener;
}

/**
 * Set up event handlers for the popup
 * @param {HTMLElement} popup - Popup element
 */
function setupPopupEventHandlers(popup) {
    // Volume Profile toggle
    const vpToggle = popup.querySelector('#chart-vp-toggle');
    if (vpToggle) {
        vpToggle.onclick = () => {
            const chartObj = window.chartRegistry?.[window.selectedChartIndex || 0];
            if (chartObj?.volumeProfile?.isEnabled) {
                const enabled = chartObj.volumeProfile.isEnabled();
                chartObj.volumeProfile.setEnabled(!enabled);
                document.dispatchEvent(new CustomEvent('volumeProfileToggled', { 
                    detail: { index: window.selectedChartIndex || 0, enabled: !enabled } 
                }));
            }
        };
    }
    
    // COT toggle
    const cotToggle = popup.querySelector('#cot-toggle');
    if (cotToggle) {
        cotToggle.onclick = () => {
            const isActive = cotToggle.classList.contains('active');
            cotToggle.classList.toggle('active');
            
            // Apply the setting immediately
            document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
                detail: { 
                    showCOT: !isActive
                }
            }));
        };
    }
    
    // Absorption toggle
    const absorptionToggle = popup.querySelector('#absorption-toggle');
    if (absorptionToggle) {
        absorptionToggle.onclick = () => {
            const isActive = absorptionToggle.classList.contains('active');
            absorptionToggle.classList.toggle('active');
            
            // Apply the setting immediately
            document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
                detail: { 
                    showAbsorption: !isActive
                }
            }));
        };
    }
    
    // Auto-apply threshold settings on blur/enter without closing popup
    const thresholdInputs = popup.querySelectorAll('#imbalance-threshold-input, #severe-imbalance-threshold-input, #volume-threshold-input, #low-volume-threshold-input, #vp-bars-input, #value-area-input, #absorption-volume-input, #absorption-price-input');
    
    function applyThresholdSettings() {
        try {
            const imbalanceThreshold = parseInt(document.getElementById('imbalance-threshold-input').value);
            const severeImbalanceThreshold = parseInt(document.getElementById('severe-imbalance-threshold-input').value);
            const imbalanceVolumeThreshold = parseInt(document.getElementById('volume-threshold-input').value);
            const lowVolumeThreshold = parseInt(document.getElementById('low-volume-threshold-input').value);
            const volumeProfileBars = parseInt(document.getElementById('vp-bars-input').value);
            const valueAreaPercentage = parseInt(document.getElementById('value-area-input').value);
            const absorptionVolumeThreshold = parseInt(document.getElementById('absorption-volume-input').value);
            const absorptionPriceThreshold = parseFloat(document.getElementById('absorption-price-input').value);
            
            // Get COT toggle state
            const cotToggleBtn = document.getElementById('cot-toggle');
            const showCOT = cotToggleBtn ? cotToggleBtn.classList.contains('active') : true;
            
            // Get absorption toggle state
            const absorptionToggleBtn = document.getElementById('absorption-toggle');
            const showAbsorption = absorptionToggleBtn ? absorptionToggleBtn.classList.contains('active') : true;
            
            // Get current settings from localStorage for navbar toggles
            const currentSettings = JSON.parse(localStorage.getItem('chartSettings') || '{}');
            const showLowVolume = currentSettings.showLowVolume !== false;
            const volumeDisplayMode = currentSettings.volumeDisplayMode || 'split';
            
            // Dispatch event to apply threshold settings to charts
            document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
                detail: { 
                    imbalanceThreshold, 
                    severeImbalanceThreshold, 
                    imbalanceVolumeThreshold, 
                    lowVolumeThreshold,
                    showLowVolume,
                    showCOT,
                    showAbsorption,
                    volumeDisplayMode,
                    volumeProfileBars,
                    valueAreaPercentage,
                    absorptionVolumeThreshold,
                    absorptionPriceThreshold
                }
            }));
            
            // Auto-applied threshold settings
        } catch (error) {
            console.warn('Error auto-applying threshold settings:', error);
        }
    }
    
    thresholdInputs.forEach(input => {
        // Apply on blur (click outside)
        input.addEventListener('blur', applyThresholdSettings);
        
        // Apply on Enter key
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                input.blur(); // This will trigger the blur event and apply settings
            }
        });
    });
}

/**
 * Show settings popup dialog
 */
export function showSettingsPopup() {
    // Remove existing popup if any
    const existingPopup = document.getElementById('settings-popup');
    if (existingPopup) {
        existingPopup.remove();
        return;
    }

    // Get current settings (reload from localStorage to ensure freshness)
    loadSettings();
    const settings = getSettings();
    
    // Load chart-specific settings for the current chart
    try {
        const selectedIdx = window.selectedChartIndex || 0;
        const chartId = 'chart-' + selectedIdx;
        const chartObj = window.chartRegistry?.[selectedIdx];
        
        // Load saved chart-specific settings
        const chartSettings = localStorage.getItem('chartSettings_' + chartId);
        if (chartSettings) {
            const obj = JSON.parse(chartSettings);
            // Merge ALL chart-specific settings
            settings.imbalanceVolumeThreshold = obj.imbalanceVolumeThreshold || settings.imbalanceVolumeThreshold;
            settings.imbalanceThreshold = obj.imbalanceThreshold || settings.imbalanceThreshold;
            settings.severeImbalanceThreshold = obj.severeImbalanceThreshold || settings.severeImbalanceThreshold;
            settings.lowVolumeThreshold = obj.lowVolumeThreshold || settings.lowVolumeThreshold;
            // Volume Profile settings
            settings.volumeProfileBars = obj.volumeProfileBars || settings.volumeProfileBars;
            settings.valueAreaPercentage = obj.valueAreaPercentage || settings.valueAreaPercentage;
            // Absorption Detection settings
            settings.absorptionVolumeThreshold = obj.absorptionVolumeThreshold || settings.absorptionVolumeThreshold;
            settings.absorptionPriceThreshold = obj.absorptionPriceThreshold || settings.absorptionPriceThreshold;
        }
        
        // Try to get live values from chart plugins if available
        if (chartObj) {
            // Get current Volume Profile settings if plugin exists
            if (chartObj.volumeProfile && chartObj.volumeProfile._options) {
                const vpOptions = chartObj.volumeProfile._options;
                if (vpOptions.numberOfRows !== undefined) settings.volumeProfileBars = vpOptions.numberOfRows;
                if (vpOptions.valueAreaPercentage !== undefined) settings.valueAreaPercentage = vpOptions.valueAreaPercentage;
            }
            
            // Get current Footprint absorption settings if plugin exists
            if (chartObj.footprintSeries && chartObj.footprintSeries._options) {
                const fpOptions = chartObj.footprintSeries._options;
                if (fpOptions.absorptionVolumeThreshold !== undefined) settings.absorptionVolumeThreshold = fpOptions.absorptionVolumeThreshold;
                if (fpOptions.absorptionPriceThreshold !== undefined) settings.absorptionPriceThreshold = fpOptions.absorptionPriceThreshold;
                if (fpOptions.imbalanceVolumeThreshold !== undefined) settings.imbalanceVolumeThreshold = fpOptions.imbalanceVolumeThreshold;
                if (fpOptions.imbalanceThreshold !== undefined) settings.imbalanceThreshold = fpOptions.imbalanceThreshold;
                if (fpOptions.severeImbalanceThreshold !== undefined) settings.severeImbalanceThreshold = fpOptions.severeImbalanceThreshold;
                if (fpOptions.lowVolumeThreshold !== undefined) settings.lowVolumeThreshold = fpOptions.lowVolumeThreshold;
                if (fpOptions.showLowVolume !== undefined) settings.showLowVolume = fpOptions.showLowVolume;
                if (fpOptions.showCOT !== undefined) settings.showCOT = fpOptions.showCOT;
                if (fpOptions.showAbsorption !== undefined) settings.showAbsorption = fpOptions.showAbsorption;
                if (fpOptions.volumeDisplayMode !== undefined) settings.volumeDisplayMode = fpOptions.volumeDisplayMode;
            }
            
            // Set chart type if available
            if (chartObj.chartType) {
                settings.chartType = chartObj.chartType;
            }
        }
    } catch (error) {
        console.warn('Error loading chart-specific settings:', error);
    }
    
    // Showing settings popup with current active chart settings

    // Create settings popup
    const popup = document.createElement('div');
    popup.id = 'settings-popup';
    popup.className = 'settings-popup';
    popup.innerHTML = createSettingsPopupHTML(settings);
    
    document.body.appendChild(popup);
    
    // Position popup near settings button
    positionSettingsPopup(popup);
    
    // Add animation
    setTimeout(() => popup.classList.add('visible'), 10);
    
    // Set up interactions
    setupPopupEventHandlers(popup);
    setupKeyboardEventHandlers(popup);
    setupChartSelectionListener(popup);
}

// Function to apply settings from popup
window.applySettingsFromPopup = function() {
    const newSettings = {
        timezone: document.getElementById('timezone-selector').value,
        timeFormat: document.getElementById('time-format-selector').value,
        // Chart threshold settings
        imbalanceThreshold: parseInt(document.getElementById('imbalance-threshold-input').value),
        severeImbalanceThreshold: parseInt(document.getElementById('severe-imbalance-threshold-input').value),
        imbalanceVolumeThreshold: parseInt(document.getElementById('volume-threshold-input').value),
        lowVolumeThreshold: parseInt(document.getElementById('low-volume-threshold-input').value),
        // Volume Profile settings
        volumeProfileBars: parseInt(document.getElementById('vp-bars-input').value),
        valueAreaPercentage: parseInt(document.getElementById('value-area-input').value),
        // Absorption Detection settings
        absorptionVolumeThreshold: parseInt(document.getElementById('absorption-volume-input').value),
        absorptionPriceThreshold: parseFloat(document.getElementById('absorption-price-input').value)
    };
    
    // Get COT toggle state
    const cotToggleBtn = document.getElementById('cot-toggle');
    newSettings.showCOT = cotToggleBtn ? cotToggleBtn.classList.contains('active') : true;
    
    // Get absorption toggle state
    const absorptionToggleBtn = document.getElementById('absorption-toggle');
    newSettings.showAbsorption = absorptionToggleBtn ? absorptionToggleBtn.classList.contains('active') : true;
    
    // Get current settings from localStorage for navbar toggles
    const currentSettings = JSON.parse(localStorage.getItem('chartSettings') || '{}');
    newSettings.showLowVolume = currentSettings.showLowVolume !== false;
    newSettings.volumeDisplayMode = currentSettings.volumeDisplayMode || 'split';
    
    if (saveSettings(newSettings)) {
        // Settings saved
        
        // Dispatch chart settings changed event for threshold values
        document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
            detail: { 
                imbalanceVolumeThreshold: newSettings.imbalanceVolumeThreshold,
                imbalanceThreshold: newSettings.imbalanceThreshold,
                severeImbalanceThreshold: newSettings.severeImbalanceThreshold,
                lowVolumeThreshold: newSettings.lowVolumeThreshold,
                showLowVolume: newSettings.showLowVolume,
                showCOT: newSettings.showCOT,
                showAbsorption: newSettings.showAbsorption,
                volumeDisplayMode: newSettings.volumeDisplayMode,
                volumeProfileBars: newSettings.volumeProfileBars,
                valueAreaPercentage: newSettings.valueAreaPercentage,
                absorptionVolumeThreshold: newSettings.absorptionVolumeThreshold,
                absorptionPriceThreshold: newSettings.absorptionPriceThreshold
            }
        }));
        
        // Apply chart type change if needed
        const chartType = document.getElementById('chart-type-select').value;
        if (chartType) {
            document.dispatchEvent(new CustomEvent('chartTypeSwitch', { detail: { type: chartType } }));
        }
    }
    const popup = document.getElementById('settings-popup');
    // Remove chart selection listener if it exists
    if (popup && popup._chartSelectionListener) {
        document.removeEventListener('chartSelected', popup._chartSelectionListener);
    }
    popup.remove();
};

// Function to reset all settings to default values
window.resetSettingsToDefault = function() {
    // Confirm reset action
    if (!confirm('Reset all settings to default values? This action cannot be undone.')) {
        return;
    }
    
    // Reset input fields to default values
    document.getElementById('timezone-selector').value = DEFAULT_SETTINGS.timezone;
    document.getElementById('time-format-selector').value = DEFAULT_SETTINGS.timeFormat;
    
    // Reset threshold settings
    document.getElementById('imbalance-threshold-input').value = DEFAULT_SETTINGS.imbalanceThreshold;
    document.getElementById('severe-imbalance-threshold-input').value = DEFAULT_SETTINGS.severeImbalanceThreshold;
    document.getElementById('volume-threshold-input').value = DEFAULT_SETTINGS.imbalanceVolumeThreshold;
    document.getElementById('low-volume-threshold-input').value = DEFAULT_SETTINGS.lowVolumeThreshold;
    
    // Reset COT toggle
    const cotToggle = document.getElementById('cot-toggle');
    if (cotToggle) {
        if (DEFAULT_SETTINGS.showCOT) {
            cotToggle.classList.add('active');
        } else {
            cotToggle.classList.remove('active');
        }
    }
    
    // Reset absorption toggle
    const absorptionToggle = document.getElementById('absorption-toggle');
    if (absorptionToggle) {
        if (DEFAULT_SETTINGS.showAbsorption) {
            absorptionToggle.classList.add('active');
        } else {
            absorptionToggle.classList.remove('active');
        }
    }
    
    // Reset Volume Profile settings
    document.getElementById('vp-bars-input').value = DEFAULT_SETTINGS.volumeProfileBars;
    document.getElementById('value-area-input').value = DEFAULT_SETTINGS.valueAreaPercentage;
    
    // Reset Absorption Detection settings
    document.getElementById('absorption-volume-input').value = DEFAULT_SETTINGS.absorptionVolumeThreshold;
    document.getElementById('absorption-price-input').value = DEFAULT_SETTINGS.absorptionPriceThreshold;
    
    // Optionally auto-apply the reset values
    if (confirm('Apply the default settings immediately?')) {
        // Save default settings
        if (saveSettings({ ...DEFAULT_SETTINGS })) {
            // Settings reset to defaults and applied
            
            // Dispatch chart settings changed event
            document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
                detail: { 
                    imbalanceVolumeThreshold: DEFAULT_SETTINGS.imbalanceVolumeThreshold,
                    imbalanceThreshold: DEFAULT_SETTINGS.imbalanceThreshold,
                    severeImbalanceThreshold: DEFAULT_SETTINGS.severeImbalanceThreshold,
                    lowVolumeThreshold: DEFAULT_SETTINGS.lowVolumeThreshold,
                    showLowVolume: DEFAULT_SETTINGS.showLowVolume,
                    showCOT: DEFAULT_SETTINGS.showCOT,
                    showAbsorption: DEFAULT_SETTINGS.showAbsorption,
                    volumeDisplayMode: DEFAULT_SETTINGS.volumeDisplayMode,
                    volumeProfileBars: DEFAULT_SETTINGS.volumeProfileBars,
                    valueAreaPercentage: DEFAULT_SETTINGS.valueAreaPercentage,
                    absorptionVolumeThreshold: DEFAULT_SETTINGS.absorptionVolumeThreshold,
                    absorptionPriceThreshold: DEFAULT_SETTINGS.absorptionPriceThreshold
                }
            }));
            
            // Close popup after applying
            const popup = document.getElementById('settings-popup');
            // Remove chart selection listener if it exists
            if (popup && popup._chartSelectionListener) {
                document.removeEventListener('chartSelected', popup._chartSelectionListener);
            }
            popup.remove();
        }
    }
};

// Function to close settings popup with proper cleanup
window.closeSettingsPopup = function() {
    const popup = document.getElementById('settings-popup');
    if (popup) {
        // Remove chart selection listener if it exists
        if (popup._chartSelectionListener) {
            document.removeEventListener('chartSelected', popup._chartSelectionListener);
        }
        popup.remove();
    }
};

// Inject settings-related CSS styles
export function injectSettingsStyles() {
    const style = document.createElement('style');
    style.id = 'settings-styles';
    style.innerHTML = `
        /* Settings Popup Styles */
        .settings-popup {
            position: fixed;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
            z-index: 1001;
            min-width: 280px;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            pointer-events: none;
        }
        
        .settings-popup.visible {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }
        
        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-element);
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }
        
        .settings-header h3 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
            transition: all 0.15s ease;
        }
        
        .close-btn:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
        }
        
        .settings-content {
            padding: 16px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .setting-group {
            margin-bottom: 16px;
        }
        
        .setting-group:last-child {
            margin-bottom: 0;
        }
        
        .setting-group > label:first-child {
            display: block;
            font-size: 12px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 6px;
        }
        
        .setting-group select {
            width: 100%;
            background: var(--bg-element);
            border: 1px solid var(--border-color);
            border-radius: 3px;
            color: var(--text-primary);
            padding: 6px 8px;
            font-size: 12px;
            outline: none;
            transition: border-color 0.15s ease;
        }
        
        .setting-group select:focus {
            border-color: var(--accent-blue);
        }
        
        .checkbox-wrapper {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 12px;
            color: var(--text-primary);
        }
        
        .checkbox-wrapper input[type="checkbox"] {
            display: none;
        }
        
        .checkmark {
            width: 16px;
            height: 16px;
            background: var(--bg-element);
            border: 1px solid var(--border-color);
            border-radius: 2px;
            margin-right: 8px;
            position: relative;
            transition: all 0.15s ease;
        }
        
        .checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
            background: var(--accent-blue);
            border-color: var(--accent-blue);
        }
        
        .checkbox-wrapper input[type="checkbox"]:checked + .checkmark:after {
            content: "✓";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
            font-weight: bold;
        }
        
        .settings-footer {
            padding: 12px 16px;
            border-top: 1px solid var(--border-color);
            background: var(--bg-element);
            border-bottom-left-radius: 6px;
            border-bottom-right-radius: 6px;
            display: flex;
            gap: 8px;
        }
        
        .apply-btn, .reset-btn {
            flex: 1;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.15s ease;
        }
        
        .apply-btn {
            background: var(--accent-blue);
            color: white;
        }
        
        .apply-btn:hover {
            background: #1e53e5;
        }
        
        .reset-btn {
            background: var(--bg-secondary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }
        
        .reset-btn:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
            border-color: var(--text-secondary);
        }
        
        /* Legend Options Styling */
        .legend-options {
            transition: all 0.2s ease;
        }
        
        .legend-options .checkbox-wrapper {
            font-size: 11px;
            color: var(--text-secondary);
        }
        
        .legend-options .checkmark {
            width: 14px;
            height: 14px;
            margin-right: 6px;
        }
        
        /* Threshold Settings Styles */
        .threshold-row, .display-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            min-height: 28px;
        }
        
        .threshold-row label, .display-row label {
            font-size: 11px;
            color: var(--text-secondary);
            margin: 0;
            flex: 0 0 120px; /* Fixed width for consistent alignment */
        }
        
        .threshold-row .controls-group {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            justify-content: flex-end;
        }
        
        .threshold-row input {
            width: 80px;
            font-size: 11px;
            padding: 4px 6px;
            height: 26px;
            box-sizing: border-box;
            border-radius: 2px;
            border: 1px solid var(--border-color);
            background: var(--bg-element);
            color: var(--text-primary);
            text-align: right;
            -moz-appearance: textfield; /* Firefox */
        }
        
        /* Hide number input spinners in Chrome, Safari, Edge */
        .threshold-row input[type="number"]::-webkit-outer-spin-button,
        .threshold-row input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        
        .display-row select {
            width: 80px;
            font-size: 11px;
            padding: 2px 4px;
            height: 26px;
            border-radius: 2px;
            border: 1px solid var(--border-color);
            background: var(--bg-element);
            color: var(--text-primary);
        }
        
        .toggle-btn {
            width: 80px;
            height: 26px;
            font-size: 11px;
            border-radius: 2px;
            border: 1px solid var(--border-color);
            background: var(--bg-element);
            color: var(--text-primary);
            cursor: pointer;
            transition: background 0.15s ease;
        }
        
        .toggle-btn:hover {
            background: var(--bg-hover);
        }
        
        .small-toggle-btn {
            width: 28px;
            height: 14px;
            padding: 0;
            margin: 0 4px;
            border-radius: 10px;
            border: 1px solid var(--border-color, #444);
            background: var(--bg-element, #333);
            cursor: pointer;
            transition: all 0.25s ease;
            display: inline-flex;
            align-items: center;
            justify-content: flex-start;
            position: relative;
            overflow: hidden;
        }

        .small-toggle-btn::before {
            content: '';
            width: 10px;
            height: 10px;
            border-radius: 50%;
            /* Use a theme-aware knob color for contrast */
            background: var(--bg-secondary, #fff);
            transition: all 0.25s ease;
            transform: translateX(1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        /* Hover uses the primary accent (blue) from the theme */
        .small-toggle-btn:hover {
            border-color: var(--accent-blue, #2962FF);
        }

        /* Active state should clearly reflect the theme accent */
        .small-toggle-btn.active {
            background: var(--accent-blue, #2962FF);
            border-color: var(--accent-blue, #2962FF);
        }

        .small-toggle-btn.active::before {
            transform: translateX(13px);
        }
    `;
    
    document.head.appendChild(style);
}
