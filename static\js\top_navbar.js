// Navbar and style utilities for Tradelab Chartings 
// Navbar and style utilities for Tradelab Chartings 

import { showSettingsPopup, injectSettingsStyles } from './settings.js';

// Store navbar visibility state
let isNavbarVisible = true;

// Setup navbar toggle keyboard shortcuts
// Update volume profile button state based on chart object
function updateVolumeProfileButtonState(chartObj) {
    const vpBtn = document.getElementById('volume-profile-toggle');
    if (!vpBtn || !chartObj || !chartObj.volumeProfile) return;
    
    const enabled = chartObj.volumeProfile.isEnabled();
    if (enabled) {
        vpBtn.classList.add('active');
        vpBtn.style.background = 'rgba(33, 150, 243, 0.2)';
    } else {
        vpBtn.classList.remove('active');
        vpBtn.style.background = '';
    }
}

function setupNavbarToggle() {
    // Add keyboard shortcuts for navbar actions
    document.addEventListener('keydown', (e) => {
        // Ctrl+Space: Toggle navbar visibility
        if (e.ctrlKey && e.code === 'Space') {
            e.preventDefault();
            toggleNavbarVisibility();
        }
        // Ctrl+C: Open chart settings popup
        else if (e.ctrlKey && e.code === 'KeyC') {
            // Don't interfere with copy operations in input fields
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.isContentEditable) {
                return; // Allow normal copy behavior
            }
            e.preventDefault();
            // Import and call showSettingsPopup from settings.js
            if (window.chartSettings && typeof window.chartSettings.showSettingsPopup === 'function') {
                window.chartSettings.showSettingsPopup();
            } else {
                // Fallback: try to import from settings module
                import('./settings.js').then(module => {
                    if (module.showSettingsPopup) {
                        module.showSettingsPopup();
                    }
                }).catch(err => console.warn('Failed to open chart settings:', err));
            }
        }
    });
}

export function createNavbar() {
    const navbar = document.createElement('nav');
    navbar.id = 'main-navbar';
    
    // Apply inline styles for critical rendering path
    const navStyles = {
        position: 'fixed', 
        top: '0', 
        left: '0', 
        width: '100%', 
        height: '35px',
        background: '#131722', 
        color: '#D1D4DC', 
        display: 'flex', 
        alignItems: 'center',
        justifyContent: 'space-between', 
        zIndex: '1000', 
        boxShadow: '0 1px 0 #23263A',
        userSelect: 'none', 
        padding: '0',
        fontSize: '12px'
    };
    
    Object.assign(navbar.style, navStyles);
    
    // Create HTML structure for the navbar
    navbar.innerHTML = `
        <div id="navbar-left" class="navbar-section">
            <div id="symbol-info" class="symbol-container">
                <span class="search-icon">&#128269;</span>
                <span id="current-symbol" class="current-symbol"></span>
            </div>
            <div id="timeframe-selector" class="timeframe-container"></div>
        </div>
        <div id="navbar-right" class="navbar-section">
            <!-- Toggle Buttons Container -->
            <div class="navbar-toggle-container" style="display:flex;align-items:center;gap:4px;margin:0 2px;">
                <button id="navbar-low-volume-toggle" class="small-toggle-btn" title="low Volume "></button>
                <button id="navbar-volume-mode-toggle" class="small-toggle-btn" title="(Split/Delta)"></button>                
            </div>
            <div class="layout-controls">
                <div id="navbar-clock" class="navbar-clock" title="Local time (timezone aware)"></div>
                <!-- Single layout menu trigger: opens dropdown containing layout choices -->
                    <div id="layout-menu" style="position:relative; display:inline-block;">
                    <button id="layout-menu-trigger" class="layout-btn" title="Layouts">
                        <!-- simple grid icon -->
                        <span id="layout-menu-icon">▦</span>
                    </button>
                    <!-- NOTE: dropdown is created dynamically on first click to avoid flashing during load/refresh -->
                </div>
            </div>
            <div class="settings-controls" style="display:flex;align-items:center;gap:8px;">
                <button id="chart-type-switch" class="layout-btn" title="Footprint" aria-label="Footprint">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                        <!-- Refined stacked bars with better proportions and styling -->
                        <!-- Top level - balanced volumes -->
                        <rect x="4" y="5" width="7" height="3.5" fill="currentColor" opacity="0.75" rx="0.5" />
                        <rect x="13" y="5" width="7" height="3.5" fill="currentColor" opacity="0.45" rx="0.5" />
                        
                        <!-- Middle level - ask dominant -->
                        <rect x="4" y="10.25" width="5" height="3.5" fill="currentColor" opacity="0.75" rx="0.5" />
                        <rect x="11" y="10.25" width="9" height="3.5" fill="currentColor" opacity="0.45" rx="0.5" />
                        
                        <!-- Bottom level - bid dominant -->
                        <rect x="4" y="15.5" width="9" height="3.5" fill="currentColor" opacity="0.75" rx="0.5" />
                        <rect x="15" y="15.5" width="5" height="3.5" fill="currentColor" opacity="0.45" rx="0.5" />
                        
                        <!-- Subtle center divider -->
                        <line x1="12" y1="4" x2="12" y2="20" stroke="currentColor" stroke-width="0.8" opacity="0.25" />
                    </svg>
                </button>
                    <button id="volume-profile-toggle" class="layout-btn" title="Volume Profile (Ctrl+F)" aria-label="Volume Profile">
                        <!-- Volume profile bars pointing upward -->
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                            <!-- Horizontal base line -->
                            <line x1="4" y1="20" x2="20" y2="20" stroke-width="1" opacity="0.4" />
                            
                            <!-- Left bar - high volume -->
                            <line x1="6" y1="20" x2="6" y2="4" stroke-width="3" />
                            <!-- Middle bar - medium volume -->
                            <line x1="12" y1="20" x2="12" y2="8" stroke-width="2.5" />
                            <!-- Right bar - low volume -->
                            <line x1="18" y1="20" x2="18" y2="14" stroke-width="2" />
                            
                            <!-- Volume level markers at top of bars -->
                            <circle cx="6" cy="4" r="1" fill="currentColor" />
                            <circle cx="12" cy="8" r="1" fill="currentColor" />
                            <circle cx="18" cy="14" r="1" fill="currentColor" />
                        </svg>
                    </button>
                <button id="horizontal-line-tool-btn" class="layout-btn" title="Line(Ctrl+E)">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <!-- horizontal line -->
                        <line x1="3" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        <!-- single centered circle on the line -->
                        <circle cx="12" cy="12" r="2.5" fill="currentColor" />
                    </svg>
                </button>
                <button id="drawing-tool-btn" class="layout-btn" title="Rectangle (Ctrl+R)">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/>
                    </svg>
                </button>
                <button id="clear-drawings-btn" class="layout-btn" title="Clear Drawings" aria-label="Clear drawings">
                    <!-- Trash can icon copied from drawing-tool.js -->
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="color: #F23645;" aria-hidden="true">
                        <path d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM19 4H15.5L14.5 3H9.5L8.5 4H5V6H19V4Z"/>
                    </svg>
                </button>
                <button id="settings-btn" class="settings-btn" title="Settings">
                    <span class="settings-icon">⚙️</span>
                </button>
            </div>
        </div>
    `;
    
    document.body.prepend(navbar);
    
    // Layout button event handling will be set up in chart.js
    // to ensure proper state management and persistence

    // Setup settings button
    const settingsBtn = navbar.querySelector('#settings-btn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', showSettingsPopup);
    }

    // Chart type switcher button: normal click toggles chart type, shift+click opens settings popup
    const chartTypeBtn = navbar.querySelector('#chart-type-switch');
    if (chartTypeBtn) {
        chartTypeBtn.addEventListener('click', (e) => {
            if (e.shiftKey) {
                // Show footprint settings popup
                let popup = document.getElementById('chart-settings-popup');
                if (popup) return; // Already open
                popup = document.createElement('div');
                popup.id = 'chart-settings-popup';
                popup.style.position = 'absolute';
                popup.style.visibility = 'hidden'; // Hide while we calculate position
                popup.style.background = '#23263A';
                popup.style.color = '#B2B5BE';
                popup.style.border = '1px solid #23263A';
                popup.style.borderRadius = '8px';
                popup.style.padding = '10px 10px 8px 10px';
                popup.style.zIndex = '2000';
                popup.style.boxShadow = '0 2px 16px rgba(0,0,0,0.18)';
                popup.style.width = '190px';
                popup.style.minWidth = '0';
                popup.style.maxWidth = '90vw';
                popup.innerHTML = `
                    <div style="font-size:13px;font-weight:600;margin-bottom:8px;text-align:center;">Footprint Settings</div>
                    <div style="margin-bottom:8px;display:flex;flex-direction:column;gap:6px;">
                        <div style="display:flex;align-items:center;justify-content:space-between;">
                            <label for="popup-bucket-size-input" style="font-size:11px;color:#B2B5BE;">Tick Size:</label>
                            <input id="popup-bucket-size-input" type="number" step="0.01" min="0.01" value="0.05" style="width:72px;max-width:72px;font-size:11px;padding:4px 6px;height:26px;box-sizing:border-box;border-radius:2px;border:1px solid #363C4E;background:#181A20;color:#D1D4DC;text-align:right;">
                        </div>
                        <div style="display:flex;align-items:center;justify-content:space-between;">
                            <label for="popup-multiplier-input" style="font-size:11px;color:#B2B5BE;">Multiplier</label>
                            <input id="popup-multiplier-input" type="number" step="1" min="1" value="100" style="width:72px;max-width:72px;font-size:11px;padding:4px 6px;height:26px;box-sizing:border-box;border-radius:2px;border:1px solid #363C4E;background:#181A20;color:#D1D4DC;text-align:right;">
                        </div>
                    </div>
                    <div style="display:flex;justify-content:flex-end;gap:6px;">
                        <button id="close-chart-settings-btn" style="background:#363C4E;color:#fff;border:none;border-radius:2px;padding:4px 10px;font-size:11px;cursor:pointer;">Close</button>
                        <button id="apply-chart-settings-btn" style="background:#2962FF;color:#fff;border:none;border-radius:2px;padding:4px 10px;font-size:11px;cursor:pointer;">Apply</button>
                    </div>
                `;
                document.body.appendChild(popup);
                
                // Position popup near the button, ensuring it stays within viewport bounds
                const rect = chartTypeBtn.getBoundingClientRect();
                const margin = 8; // Minimum margin from screen edges
                
                // Get actual popup dimensions after it's been added to DOM
                const popupRect = popup.getBoundingClientRect();
                const popupWidth = popupRect.width;
                const popupHeight = popupRect.height;
                
                // Calculate initial position (below the button)
                let left = rect.left;
                let top = rect.bottom + 6;
                
                // Adjust horizontal position if popup would overflow right edge
                if (left + popupWidth > window.innerWidth - margin) {
                    left = rect.right - popupWidth; // Align right edge with button's right edge
                }
                
                // Ensure popup doesn't go off the left edge
                if (left < margin) {
                    left = margin;
                }
                
                // Adjust vertical position if popup would overflow bottom edge
                if (top + popupHeight > window.innerHeight - margin) {
                    // Try positioning above the button instead
                    const topAbove = rect.top - popupHeight - 6;
                    if (topAbove >= margin) {
                        top = topAbove;
                    } else {
                        // If it doesn't fit above either, clamp to screen bottom
                        top = window.innerHeight - popupHeight - margin;
                    }
                }
                
                // Ensure popup doesn't go off the top edge
                if (top < margin) {
                    top = margin;
                }
                
                // Apply final position and make visible
                popup.style.top = `${top}px`;
                popup.style.left = `${left}px`;
                popup.style.visibility = 'visible';
                
                // Set current values from chart (if available)
                const bucketInput = popup.querySelector('#popup-bucket-size-input');
                const multiplierInput = popup.querySelector('#popup-multiplier-input');
                // Prefill with current chart settings from chart.js
                try {
                    // Ensure we get the correct selected chart index
                    let selectedIdx = window.selectedChartIndex || 0;
                    
                    // Get the selected chart ID using the correct index
                    const chartId = 'chart-' + selectedIdx;
                    
                    // Define function to update inputs with chart settings
                    function updateFootprintInputs(chartId) {
                        // Get the chart index from the chartId
                        const idx = parseInt(chartId.split('-')[1]);
                        
                        // First try to get current values from the active chart object
                        if (window.chartRegistry && window.chartRegistry[idx]) {
                            const chartObj = window.chartRegistry[idx];
                            if (typeof chartObj.bucket_size === 'number') bucketInput.value = chartObj.bucket_size;
                            if (typeof chartObj.multiplier === 'number') multiplierInput.value = chartObj.multiplier;
                            return; // Successfully updated from chart object
                        }
                        
                        // Fallback to localStorage if chart object not available
                        const saved = localStorage.getItem('chartSettings_' + chartId);
                        if (saved) {
                            const obj = JSON.parse(saved);
                            if (typeof obj.bucket_size === 'number') bucketInput.value = obj.bucket_size;
                            if (typeof obj.multiplier === 'number') multiplierInput.value = obj.multiplier;
                        }
                    }
                    
                    // Initial update with current chart settings
                    updateFootprintInputs(chartId);
                    
                    // Listen for chart selection changes while popup is open
                    function chartSelectionListener(e) {
                        // Update inputs when selected chart changes
                        const newChartId = 'chart-' + (window.selectedChartIndex || 0);
                        updateFootprintInputs(newChartId);
                    }
                    
                    // Add event listener for chart selection changes
                    document.addEventListener('chartSelected', chartSelectionListener);
                    
                    // Store the listener so we can remove it when popup closes
                    popup._chartSelectionListener = chartSelectionListener;
                    
                } catch (err) {
                    console.warn('Error loading chart settings for footprint popup:', err);
                }
                // Apply button
                popup.querySelector('#apply-chart-settings-btn').onclick = () => {
                    const bucket_size = parseFloat(bucketInput.value);
                    const multiplier = parseInt(multiplierInput.value);
                    document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
                        detail: { bucket_size, multiplier }
                    }));
                    popup.remove();
                    document.removeEventListener('keydown', escListener);
                    // Remove chart selection listener if it exists
                    if (popup._chartSelectionListener) {
                        document.removeEventListener('chartSelected', popup._chartSelectionListener);
                    }
                };
                // Close button
                popup.querySelector('#close-chart-settings-btn').onclick = () => {
                    popup.remove();
                    document.removeEventListener('keydown', escListener);
                    // Remove chart selection listener if it exists
                    if (popup._chartSelectionListener) {
                        document.removeEventListener('chartSelected', popup._chartSelectionListener);
                    }
                };
                // Enter key applies settings and closes popup
                function enterListener(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const bucket_size = parseFloat(bucketInput.value);
                        const multiplier = parseInt(multiplierInput.value);
                        document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
                            detail: { bucket_size, multiplier }
                        }));
                        popup.remove();
                        document.removeEventListener('keydown', escListener);
                        document.removeEventListener('keydown', enterListener);
                        // Remove chart selection listener if it exists
                        if (popup._chartSelectionListener) {
                            document.removeEventListener('chartSelected', popup._chartSelectionListener);
                        }
                    }
                }
                document.addEventListener('keydown', enterListener);
                // ESC key closes popup
                function escListener(e) {
                    if (e.key === 'Escape') {
                        if (popup && document.body.contains(popup)) {
                            popup.remove();
                            document.removeEventListener('keydown', escListener);
                            document.removeEventListener('keydown', enterListener);
                            // Remove chart selection listener if it exists
                            if (popup._chartSelectionListener) {
                                document.removeEventListener('chartSelected', popup._chartSelectionListener);
                            }
                        }
                    }
                }
                document.addEventListener('keydown', escListener);
            } else {
                // Normal click: toggle chart type
                document.dispatchEvent(new CustomEvent('chartTypeSwitch', {}));
            }
        });
    }

    // Volume Profile toggle button handler
    const vpToggleBtn = navbar.querySelector('#volume-profile-toggle');
    if (vpToggleBtn) {
        vpToggleBtn.addEventListener('click', () => {
            const chartObj = window.chartRegistry?.[window.selectedChartIndex || 0];
            if (!chartObj || !chartObj.volumeProfile || !chartObj.volumeProfile.isEnabled) return;
            const enabled = chartObj.volumeProfile.isEnabled();
            chartObj.volumeProfile.setEnabled(!enabled);
            document.dispatchEvent(new CustomEvent('volumeProfileToggled', {
                detail: { index: window.selectedChartIndex || 0, enabled: !enabled }
            }));
        });
    }

    // Setup drawing tool button
    const drawingToolBtn = navbar.querySelector('#drawing-tool-btn');
    const horizontalLineBtn = navbar.querySelector('#horizontal-line-tool-btn');

    if (drawingToolBtn) {
        drawingToolBtn.addEventListener('click', () => {
            const chartObj = window.chartRegistry?.[window.selectedChartIndex || 0];
            if (chartObj?.rectangleDrawingTool) {
                // If horizontal line mode is active, disable rectangle tool
                if (horizontalLineBtn?.classList.contains('active')) {
                    return; // Do nothing if horizontal line tool is active
                }
                chartObj.rectangleDrawingTool.setDrawingMode('rectangle');
                if (chartObj.rectangleDrawingTool.isDrawing()) {
                    chartObj.rectangleDrawingTool.stopDrawing();
                    drawingToolBtn.classList.remove('active');
                    drawingToolBtn.style.background = '';
                } else {
                    chartObj.rectangleDrawingTool.startDrawing();
                    drawingToolBtn.classList.add('active');
                    drawingToolBtn.style.background = '#2962FF';
                }
            }
            
        });
    }

    if (horizontalLineBtn) {
        horizontalLineBtn.addEventListener('click', () => {
            const chartObj = window.chartRegistry?.[window.selectedChartIndex || 0];
            if (chartObj?.rectangleDrawingTool) {
                // If rectangle tool is active, disable horizontal line tool
                if (drawingToolBtn?.classList.contains('active')) {
                    return; // Do nothing if rectangle tool is active
                }
                chartObj.rectangleDrawingTool.setDrawingMode('horizontalLine');
                if (chartObj.rectangleDrawingTool.isDrawing()) {
                    chartObj.rectangleDrawingTool.stopDrawing();
                    horizontalLineBtn.classList.remove('active');
                    horizontalLineBtn.style.background = '';
                } else {
                    chartObj.rectangleDrawingTool.startDrawing();
                    horizontalLineBtn.classList.add('active');
                    horizontalLineBtn.style.background = '#2962FF';
                }
            }
        });
    }

    // Register clear-drawings button once (outside other handlers) so it always works
    const clearDrawingsBtn = navbar.querySelector('#clear-drawings-btn');
    if (clearDrawingsBtn) {
        clearDrawingsBtn.addEventListener('click', () => {
            const chartObj = window.chartRegistry?.[window.selectedChartIndex || 0];
            if (!chartObj || !chartObj.rectangleDrawingTool) return;
            const symbol = chartObj.symbol || chartObj?.container?.dataset?.symbol || 'current symbol';
            if (!confirm(`Clear all drawings for ${symbol}? This cannot be undone.`)) return;
            try {
                chartObj.rectangleDrawingTool.clearAllDrawings();
                // Update UI buttons
                document.dispatchEvent(new CustomEvent('chartSelected'));
            } catch (err) {
                console.warn('Failed to clear drawings via navbar button', err);
            }
        });
    }

    // Setup navbar volume mode toggle
    const navbarVolumeModeToggle = navbar.querySelector('#navbar-volume-mode-toggle');
    if (navbarVolumeModeToggle) {
        navbarVolumeModeToggle.addEventListener('click', () => {
            const isActive = navbarVolumeModeToggle.classList.contains('active');
            navbarVolumeModeToggle.classList.toggle('active');
            
            // Apply the setting immediately
            document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
                detail: { 
                    volumeDisplayMode: isActive ? 'split' : 'delta'
                }
            }));
        });
    }

    // Setup navbar low volume toggle
    const navbarLowVolumeToggle = navbar.querySelector('#navbar-low-volume-toggle');
    if (navbarLowVolumeToggle) {
        navbarLowVolumeToggle.addEventListener('click', () => {
            navbarLowVolumeToggle.classList.toggle('active');
            
            // Apply the setting immediately
            document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
                detail: { 
                    showLowVolume: navbarLowVolumeToggle.classList.contains('active')
                }
            }));
        });
    }

    // Setup layout menu dropdown toggle and outside-click handler
    const layoutMenuTrigger = navbar.querySelector('#layout-menu-trigger');
    // The dropdown will be created dynamically on first click to avoid flashing during load/refresh
    let layoutMenuDropdown = null;
    let layoutDropdownDocClickHandler = null;

        if (layoutMenuTrigger) {
            layoutMenuTrigger.addEventListener('click', (e) => {
                e.stopPropagation();

                // Create dropdown lazily
                if (!layoutMenuDropdown) {
                    layoutMenuDropdown = document.createElement('div');
                    layoutMenuDropdown.id = 'layout-menu-dropdown';
                    layoutMenuDropdown.className = 'layout-menu-dropdown';
                    Object.assign(layoutMenuDropdown.style, {
                        display: 'none',
                        position: 'fixed',
                        background: '#1B1D22',
                        border: '1px solid #2E323B',
                        padding: '6px',
                        zIndex: '200',
                        flexDirection: 'row',
                        gap: '8px',
                        borderRadius: '6px'
                    });

                    // Keep inner buttons with original ids so chart.js handlers remain compatible
                    const defs = [
                        { id: 'layout-1', title: 'Single Chart', text: '1' },
                        { id: 'layout-2', title: 'Two Charts Side by Side', text: '2' },
                        { id: 'layout-3', title: 'Three Charts Row', text: '3' },
                        { id: 'layout-4', title: 'Four Charts Grid', text: '4' },
                        { id: 'layout-5', title: 'Split left / right split (50/50 left, right split)', text: 'S' }
                    ];

                    defs.forEach(d => {
                        const btn = document.createElement('button');
                        btn.id = d.id;
                        btn.className = 'layout-btn';
                        btn.title = d.title;
                        btn.textContent = d.text;
                        // Dispatch an event that chart.js listens for so layout switching works even though
                        // the buttons are created dynamically after chart.js initialized.
                        btn.addEventListener('click', (ev) => {
                            ev.stopPropagation();
                            const parts = d.id.split('-');
                            const num = parseInt(parts[1], 10) || 1;
                            if (num === 5) {
                                document.dispatchEvent(new CustomEvent('requestLayoutVariant', { detail: { numCharts: 3, variant: 'split-right' } }));
                            } else {
                                document.dispatchEvent(new CustomEvent('requestLayoutVariant', { detail: { numCharts: num } }));
                            }
                            layoutMenuDropdown.style.display = 'none';
                        });
                        layoutMenuDropdown.appendChild(btn);
                    });

                    // Prevent clicks inside from bubbling to document
                    layoutMenuDropdown.addEventListener('click', (ev) => ev.stopPropagation());

                    document.body.appendChild(layoutMenuDropdown);
                    // Defensive: ensure dropdown is hidden after creation so first click opens it
                    layoutMenuDropdown.style.display = 'none';

                    // Outside click closes dropdown
                    layoutDropdownDocClickHandler = (ev) => {
                        if (!layoutMenuDropdown) return;
                        if (ev.target === layoutMenuTrigger) return;
                        if (!layoutMenuDropdown.contains(ev.target)) {
                            layoutMenuDropdown.style.display = 'none';
                        }
                    };
                    document.addEventListener('click', layoutDropdownDocClickHandler);
                }

                const isOpen = layoutMenuDropdown.style.display !== 'none';
                layoutMenuDropdown.style.display = isOpen ? 'none' : 'flex';
                layoutMenuDropdown.style.flexDirection = 'row';
                layoutMenuDropdown.style.gap = '6px';
                // position dropdown vertically under the trigger and align top-left corner with the trigger
                const rect = layoutMenuTrigger.getBoundingClientRect();
                // set top and left using viewport coordinates (position:fixed)
                layoutMenuDropdown.style.top = `${rect.bottom + 6}px`;
                layoutMenuDropdown.style.left = `${rect.left}px`;
                // clamp to viewport so the dropdown doesn't overflow
                requestAnimationFrame(() => {
                    const dd = layoutMenuDropdown.getBoundingClientRect();
                    if (dd.right > window.innerWidth - 8) {
                        layoutMenuDropdown.style.left = `${Math.max(8, rect.right - dd.width)}px`;
                    }
                    if (dd.bottom > window.innerHeight - 8) {
                        layoutMenuDropdown.style.top = `${Math.max(8, rect.top - dd.height - 6)}px`;
                    }
                });
            });
    }

    // Listen for chart selection changes to update button states
    document.addEventListener('chartSelected', () => {
        const chartObj = window.chartRegistry?.[window.selectedChartIndex || 0];
        if (chartObj?.rectangleDrawingTool) {
            const isDrawing = chartObj.rectangleDrawingTool.isDrawing();
            const drawingMode = chartObj.rectangleDrawingTool.getDrawingMode();

            // Update rectangle tool button
            if (drawingToolBtn) {
                const isRectangleActive = isDrawing && drawingMode === 'rectangle';
                drawingToolBtn.classList.toggle('active', isRectangleActive);
                drawingToolBtn.style.background = isRectangleActive ? '#2962FF' : '';
            }

            // Update horizontal line tool button
            if (horizontalLineBtn) {
                const isHorizontalLineActive = isDrawing && drawingMode === 'horizontalLine';
                horizontalLineBtn.classList.toggle('active', isHorizontalLineActive);
                horizontalLineBtn.style.background = isHorizontalLineActive ? '#2962FF' : '';
            }
        } else {
            // No drawing tool available, deactivate both buttons
            if (drawingToolBtn) {
                drawingToolBtn.classList.remove('active');
                drawingToolBtn.style.background = '';
            }
            if (horizontalLineBtn) {
                horizontalLineBtn.classList.remove('active');
                horizontalLineBtn.style.background = '';
            }
        }
        // Volume profile button state sync removed
    });

    // Listen for chart selection changes to update button states
    document.addEventListener('chartSelected', (e) => {
        const { chartObj } = e.detail || {};
        updateVolumeProfileButtonState(chartObj);
    });

    // Listen for external volume profile toggle events to keep navbar button state updated
    document.addEventListener('volumeProfileToggled', (e) => {
        const { index, enabled } = e.detail || {};
        if (typeof index === 'number' && index === (window.selectedChartIndex || 0)) {
            const vpBtn = document.getElementById('volume-profile-toggle');
            if (vpBtn) {
                if (enabled) {
                    vpBtn.classList.add('active');
                    vpBtn.style.background = 'rgba(33, 150, 243, 0.2)';
                } else {
                    vpBtn.classList.remove('active');
                    vpBtn.style.background = '';
                }
            }
        }
    });

    // Setup navbar toggle functionality
    setupNavbarToggle();
    
    // Initialize navbar toggle states
    updateNavbarToggles();
    
    // Initialize volume profile button state when charts are ready
    setTimeout(() => {
        const chartObj = window.chartRegistry?.[window.selectedChartIndex || 0];
        updateVolumeProfileButtonState(chartObj);
    }, 100);
}

// --- Timezone-aware navbar clock ---
function getClockSettings() {
    try {
        const obj = JSON.parse(localStorage.getItem('chartSettings') || '{}');
        return {
            timezone: obj.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC',
            use12Hour: obj.timeFormat === '12h'
        };
    } catch (e) {
        return { timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC', use12Hour: false };
    }
}

let _clockInterval = null;
function startNavbarClock() {
    const el = document.getElementById('navbar-clock');
    if (!el) return;
    if (_clockInterval) clearInterval(_clockInterval);
    const { timezone, use12Hour } = getClockSettings();

    function update() {
        const now = new Date();
        try {
            const fmt = new Intl.DateTimeFormat(undefined, {
                hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: !!use12Hour, timeZone: timezone
            });
            el.textContent = fmt.format(now);
            el.title = `${timezone} (${use12Hour ? '12h' : '24h'})`;
        } catch (e) {
            // Fallback: show local time
            el.textContent = now.toLocaleTimeString();
            el.title = timezone;
        }
    }

    update();
    _clockInterval = setInterval(update, 1000);
}

// Restart clock when settings change
document.addEventListener('settingsApplied', () => {
    startNavbarClock();
    updateNavbarToggles();
});

// Function to update navbar toggles based on current settings
function updateNavbarToggles() {
    // Get current settings (you'll need to import getCurrentSettings from settings.js)
    try {
        const settings = JSON.parse(localStorage.getItem('chartSettings') || '{}');
        
        // Update volume mode toggle
        const volumeModeToggle = document.getElementById('navbar-volume-mode-toggle');
        if (volumeModeToggle) {
            if (settings.volumeDisplayMode === 'delta') {
                volumeModeToggle.classList.add('active');
            } else {
                volumeModeToggle.classList.remove('active');
            }
        }
        
        // Update low volume toggle
        const lowVolumeToggle = document.getElementById('navbar-low-volume-toggle');
        if (lowVolumeToggle) {
            if (settings.showLowVolume !== false) {
                lowVolumeToggle.classList.add('active');
            } else {
                lowVolumeToggle.classList.remove('active');
            }
        }
    } catch (error) {
        console.warn('Failed to update navbar toggles:', error);
    }
}

// Start clock on DOM ready
document.addEventListener('DOMContentLoaded', () => {
    startNavbarClock();
});


// Function to robustly toggle navbar visibility and always restore layout correctly

export function toggleNavbarVisibility() {
    const navbar = document.getElementById('main-navbar');
    const chartsGrid = document.querySelector('.charts-grid');
    if (!navbar) return;

    isNavbarVisible = !isNavbarVisible;

    // Set navbar style
    navbar.style.transition = 'transform 0.3s cubic-bezier(.4,0,.2,1)';
    navbar.style.transform = isNavbarVisible ? 'translateY(0)' : 'translateY(-40px)';
    navbar.style.zIndex = '1000';

    // Set grid style
    if (chartsGrid) {
        chartsGrid.style.transition = 'top 0.3s cubic-bezier(.4,0,.2,1), height 0.3s cubic-bezier(.4,0,.2,1)';
        chartsGrid.style.top = isNavbarVisible ? '40px' : '0';
        chartsGrid.style.height = isNavbarVisible ? 'calc(100vh - 40px)' : '100vh';
    }

    // Remove any existing reveal buttons (cleanup)
    const revealBtn = document.getElementById('navbar-reveal-btn');
    if (revealBtn) revealBtn.remove();

    // Only one event, after transition, for chart resize
    setTimeout(() => {
        document.dispatchEvent(new CustomEvent('navbarToggled', { detail: { visible: isNavbarVisible } }));
        requestAnimationFrame(() => window.dispatchEvent(new Event('resize')));
    }, 320);
}

export function injectStyles() {
    const style = document.createElement('style');
    style.innerHTML = `
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
        
        :root {
            --bg-primary: #131722;
            --bg-secondary: #1E222D;
            --bg-element: #2A2E39;
            --bg-hover: #363C4E;
            --text-primary: #D1D4DC;
            --text-secondary: #787B86;
            --accent-blue: #2962FF;
            --accent-green: #22ab94;
            --accent-red: #f7525f;
            --border-color: #23263A;
        }
        
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 13px;
        }
        
        .charts-grid {
            position: absolute;
            top: 40px;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: calc(100vh - 40px);
            display: grid;
            background: var(--bg-primary);
            gap: 1px;
            transition: top 0.3s ease, height 0.3s ease;
        }
        
        .chart-container {
            width: 100%;
            height: 100%;
            background: var(--bg-secondary);
            overflow: hidden;
        }
        
        nav {
            user-select: none;
            background: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            border-bottom: 1px solid var(--border-color);
            height: 40px !important;
            min-height: 40px !important;
            max-height: 40px !important;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 13px;
            transition: transform 0.3s ease;
            transform: translateY(0);
        }
        
        .navbar-section {
            display: flex;
            align-items: center;
            height: 100%;
        }
        
        #navbar-left {
            padding-left: 8px;
            gap: 16px;
            flex: 1 1 auto;
        }
        
        #navbar-right {
            padding-right: 8px;
            gap: 8px;
            flex-shrink: 0;
        }
        
        .symbol-container {
            display: flex;
            align-items: center;
            gap: 6px;
            height: 100%;
            padding: 0 12px;
            cursor: pointer;
            border-right: 1px solid var(--border-color);
        }
        
        .symbol-container:hover {
            background: var(--bg-hover);
        }
        
        .search-icon {
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .current-symbol {
            font-weight: 600;
            font-size: 14px;
        }
        
        .timeframe-container {
            display: flex;
            align-items: center;
            height: 100%;
            gap: 2px;
            padding: 0 8px;
        }
        
        .layout-controls {
            display: flex;
            align-items: center;
            height: 100%;
            gap: 2px;
            padding: 0 4px 0 12px;
            border-left: 1px solid var(--border-color);
        }
        
        .settings-controls {
            display: flex;
            align-items: center;
            height: 100%;
            padding: 0 8px 0 8px;
        }
        
        /* Common button styles */
        .layout-btn,
        .tf-btn,
        .navbar-toggle-btn,
        .settings-btn {
            background: var(--bg-element);
            color: var(--text-primary);
            border: none;
            border-radius: 2px;
            cursor: pointer;
            transition: background 0.15s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .layout-btn:hover,
        .tf-btn:hover,
        .navbar-toggle-btn:hover,
        .settings-btn:hover {
            background: var(--bg-hover);
        }
        
        .layout-btn.active,
        .tf-btn.active {
            background: var(--accent-blue);
        }
        
        /* Specific styles */
        .layout-btn,
        .settings-btn {
            width: 24px;
            height: 24px;
        }
        
        .settings-icon {
            font-size: 14px;
        }
        
        .tf-btn {
            padding: 2px 8px;
        }
        
        .navbar-toggle-btn {
            padding: 1px 4px; /* reduced padding for compactness */
            min-width: 36px;
            height: 20px;
            font-size: 10px;
            font-weight: 500;
            border: 1px solid var(--border-color, #444);
        }
        
        .navbar-toggle-btn .toggle-text {
            white-space: nowrap;
        }
        
        /* Small toggle buttons (same as popup style) */
        .small-toggle-btn {
            width: 24px; /* slightly smaller */
            height: 12px;
            padding: 0;
            margin: 0 2px; /* small horizontal gap */
            border-radius: 8px;
            border: 1px solid var(--border-color, #444);
            background: var(--bg-element, #333);
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: flex-start;
            position: relative;
            overflow: hidden;
        }

        .small-toggle-btn::before {
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--bg-secondary, #fff);
            transition: all 0.2s ease;
            transform: translateX(1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        
        .small-toggle-btn:hover {
            border-color: var(--accent-color, #089981);
        }
        
        .small-toggle-btn.active {
            background: var(--bg-element, #333) !important;
            border-color: var(--border-color, #444) !important;
        }
        
        .small-toggle-btn.active:hover {
            border-color: var(--accent-color, #089981) !important;
        }
        
        .small-toggle-btn.active::before {
            transform: translateX(13px);
        }
        
        /* Navbar toggle button styles */
        .navbar-toggle-container {
            display: flex;
            align-items: center;
            height: 100%;
            padding: 0 8px 0 12px;
            border-left: 1px solid var(--border-color);
        }
        
        .navbar-toggle-btn {
            width: 28px;
            height: 28px;
            transition: all 0.15s ease;
        }
        
        .navbar-toggle-btn:hover {
            transform: translateY(1px);
        }
        
        .toggle-icon {
            display: inline-block;
            transition: transform 0.2s ease;
        }
        
        .navbar-toggle-btn:hover .toggle-icon {
            transform: translateY(1px);
        }
        
        /* CSS for navbar reveal button removed */

    /* Styles for chart settings popup inputs to ensure consistent sizing and hide native spinners */
        #chart-settings-popup input[type=number] {
            -moz-appearance: textfield; /* Firefox: remove spinner */
            appearance: textfield;
            padding-right: 6px; /* space for any browser UI */
            height: 26px;
            line-height: 1;
            box-sizing: border-box;
            font-size: 11px;
            text-align: right;
            border-radius: 2px;
            border: 1px solid #363C4E;
            background: #181A20;
            color: #D1D4DC;
        }

        /* Navbar clock (styled like previous label) */
        .navbar-clock {
            font-family: monospace;
            font-size: 12px;
            color: var(--text-secondary);
            padding-left: 8px;
            padding-right: 8px;
            display: inline-flex;
            align-items: center;
            height: 100%;
            white-space: nowrap;
            min-width: 64px;
            margin-right: 6px; /* similar spacing as the removed label */
        }

        /* WebKit browsers - hide the up/down arrows */
        #chart-settings-popup input[type=number]::-webkit-outer-spin-button,
        #chart-settings-popup input[type=number]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        /* Ensure labels and controls align nicely */
        #chart-settings-popup label {
            display: inline-block;
            margin-right: 8px;
            min-width: 56px;
            text-align: left;
        }
	`;
	document.head.appendChild(style);
	
	// Also inject settings styles
	injectSettingsStyles();
}
